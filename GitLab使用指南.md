# GitLab仓库创建和管理指南

## 🚀 快速开始

### 方法1: 使用自动化脚本（推荐）
1. 运行 `git_setup.bat` 脚本
2. 按照提示输入GitLab仓库信息
3. 脚本会自动完成初始化和推送

### 方法2: 手动操作
按照下面的详细步骤进行操作

## 📋 详细步骤

### 1. 在GitLab上创建新仓库

1. **登录GitLab**
   - 访问 [gitlab.com](https://gitlab.com)
   - 使用您的账号登录

2. **创建新项目**
   - 点击右上角的 "+" 按钮
   - 选择 "New project/repository"
   - 选择 "Create blank project"

3. **配置项目信息**
   ```
   Project name: **********************
   Project description: Instagram私信数据提取和分析工具
   Visibility Level: Private (推荐) 或 Public
   Initialize repository with a README: 不勾选
   ```

4. **创建项目**
   - 点击 "Create project" 按钮
   - 记录生成的仓库URL，格式如：
     `https://gitlab.com/你的用户名/**********************.git`

### 2. 本地Git配置

打开命令行（PowerShell或CMD），在项目目录中执行：

```bash
# 配置Git用户信息（如果还没配置过）
git config --global user.name "你的用户名"
git config --global user.email "你的邮箱@example.com"

# 初始化Git仓库
git init

# 添加所有文件
git add .

# 创建初始提交
git commit -m "🎉 初始提交: Instagram私信数据提取和分析工具"

# 添加远程仓库
git remote add origin https://gitlab.com/你的用户名/**********************.git

# 推送到GitLab
git branch -M main
git push -u origin main
```

### 3. 验证推送结果

1. 刷新GitLab项目页面
2. 确认所有文件已成功上传
3. 检查README.md是否正确显示

## 🔧 常见问题解决

### 问题1: 推送时要求输入用户名和密码

**解决方案A: 使用Personal Access Token（推荐）**
1. 在GitLab中创建Personal Access Token：
   - 点击右上角头像 → Settings
   - 左侧菜单选择 "Access Tokens"
   - 创建新token，权限选择 "write_repository"
2. 推送时使用token作为密码：
   - 用户名：你的GitLab用户名
   - 密码：刚创建的token

**解决方案B: 配置SSH密钥**
```bash
# 生成SSH密钥
ssh-keygen -t rsa -b 4096 -C "你的邮箱@example.com"

# 复制公钥内容
cat ~/.ssh/id_rsa.pub

# 在GitLab中添加SSH密钥：
# Settings → SSH Keys → 粘贴公钥内容
```

### 问题2: 仓库已存在内容

如果GitLab仓库已有内容，需要先拉取：
```bash
git pull origin main --allow-unrelated-histories
git push origin main
```

### 问题3: 文件过大或敏感文件

确保 `.gitignore` 文件正确配置，避免提交：
- 敏感信息（.env文件）
- 大文件（输出数据、媒体文件）
- 临时文件（日志、缓存）

## 📁 推荐的仓库结构

```
**********************/
├── 📄 README.md              # 项目说明（GitLab首页显示）
├── 📄 项目总结.md            # 详细项目总结
├── 📄 GitLab使用指南.md      # 本文件
├── 🐍 main.py               # 主程序
├── 🐍 instagram_extractor.py # 核心提取器
├── 🐍 data_analyzer.py      # 数据分析器
├── ⚙️ config.py             # 配置管理
├── 🛠️ utils.py              # 工具函数
├── 📋 requirements.txt      # 依赖包
├── 📝 .env.example          # 配置示例
├── 🚫 .gitignore            # Git忽略文件
├── 🔧 install.bat           # 安装脚本
├── ▶️ run.bat               # 运行脚本
└── 🔗 git_setup.bat         # Git设置脚本
```

## 🎯 GitLab项目优化建议

### 1. 完善项目描述
在GitLab项目设置中添加：
- 项目标签：`python`, `selenium`, `data-analysis`, `instagram`
- 项目描述：简洁明了的功能说明
- 项目头像：可以上传一个相关图标

### 2. 设置项目可见性
- **Private**: 仅自己可见（推荐，因为涉及个人数据）
- **Internal**: 登录用户可见
- **Public**: 所有人可见

### 3. 配置项目设置
- **Issues**: 启用问题跟踪
- **Wiki**: 启用项目文档
- **Snippets**: 启用代码片段
- **Container Registry**: 如需Docker支持

### 4. 添加项目徽章
在README.md中添加状态徽章：
```markdown
![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)
![License](https://img.shields.io/badge/License-Educational-green.svg)
![Platform](https://img.shields.io/badge/Platform-Windows-lightgrey.svg)
```

## 🔄 日常Git操作

### 提交新更改
```bash
# 查看文件状态
git status

# 添加修改的文件
git add .

# 提交更改
git commit -m "✨ 添加新功能: 描述具体更改"

# 推送到GitLab
git push origin main
```

### 查看提交历史
```bash
# 查看提交日志
git log --oneline

# 查看文件更改
git diff
```

### 分支管理
```bash
# 创建新分支
git checkout -b feature/new-feature

# 切换分支
git checkout main

# 合并分支
git merge feature/new-feature
```

## 🛡️ 安全注意事项

1. **永远不要提交敏感信息**：
   - Instagram登录凭据
   - 个人数据文件
   - API密钥或token

2. **使用.gitignore**：
   - 确保敏感文件被正确忽略
   - 定期检查提交内容

3. **仓库可见性**：
   - 建议设置为Private
   - 谨慎分享仓库链接

## 🎉 完成！

现在你的Instagram私信数据提取工具已经成功托管在GitLab上了！你可以：

- 📱 在任何地方访问你的代码
- 🔄 跟踪代码变更历史
- 🤝 与他人协作开发
- 📋 使用Issues跟踪问题和改进
- 📚 使用Wiki编写详细文档

记得定期提交你的更改，保持代码的版本控制！
