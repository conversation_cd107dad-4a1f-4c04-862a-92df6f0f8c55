# -*- coding: utf-8 -*-
"""
Instagram DM数据提取配置文件
"""

import os
from dotenv import load_dotenv

load_dotenv()

class Config:
    # Instagram登录信息 (请在.env文件中设置)
    INSTAGRAM_USERNAME = os.getenv('INSTAGRAM_USERNAME', '')
    INSTAGRAM_PASSWORD = os.getenv('INSTAGRAM_PASSWORD', '')
    
    # 目标用户名 (要提取对话的用户)
    TARGET_USERNAME = os.getenv('TARGET_USERNAME', '')
    
    # 浏览器配置
    CHROME_DRIVER_PATH = os.getenv('CHROME_DRIVER_PATH', '')  # 如果为空则自动下载
    HEADLESS_MODE = os.getenv('HEADLESS_MODE', 'False').lower() == 'true'
    
    # 数据提取配置
    MAX_MESSAGES = int(os.getenv('MAX_MESSAGES', '1000'))  # 最大提取消息数
    SCROLL_DELAY = float(os.getenv('SCROLL_DELAY', '2.0'))  # 滚动延迟(秒)
    LOAD_DELAY = float(os.getenv('LOAD_DELAY', '3.0'))  # 页面加载延迟(秒)
    
    # 数据存储配置
    OUTPUT_DIR = os.getenv('OUTPUT_DIR', 'output')
    BACKUP_DIR = os.getenv('BACKUP_DIR', 'backup')
    
    # 分析配置
    SENTIMENT_ANALYSIS = os.getenv('SENTIMENT_ANALYSIS', 'True').lower() == 'true'
    GENERATE_WORDCLOUD = os.getenv('GENERATE_WORDCLOUD', 'True').lower() == 'true'

    # 调试和手动模式设置
    DEBUG_MODE = os.getenv('DEBUG_MODE', 'False').lower() == 'true'
    WAIT_FOR_MANUAL_LOGIN = os.getenv('WAIT_FOR_MANUAL_LOGIN', 'True').lower() == 'true'
    MANUAL_LOGIN_TIMEOUT = int(os.getenv('MANUAL_LOGIN_TIMEOUT', '300'))
    
    # Instagram URL配置
    INSTAGRAM_BASE_URL = 'https://www.instagram.com'
    INSTAGRAM_LOGIN_URL = 'https://www.instagram.com/accounts/login/'
    INSTAGRAM_DM_URL = 'https://www.instagram.com/direct/inbox/'
    
    # 选择器配置 (Instagram页面元素选择器)
    SELECTORS = {
        'username_input': 'input[name="username"]',
        'password_input': 'input[name="password"]',
        'login_button': 'button[type="submit"]',
        'dm_button': 'a[href="/direct/inbox/"]',
        'conversation_list': 'div[role="listbox"]',
        'message_container': 'div[role="grid"]',
        'message_text': 'div[dir="auto"]',
        'message_time': 'time',
        'media_content': 'img, video',
        'load_more': 'button'
    }
    
    # 用户代理字符串
    USER_AGENT = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
