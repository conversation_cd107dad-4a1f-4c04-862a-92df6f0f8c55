@echo off
chcp 65001 >nul
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                Instagram私信数据提取工具                     ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🚀 启动Instagram私信数据提取工具...
python main.py

if %errorlevel% neq 0 (
    echo.
    echo ❌ 程序运行出错，请检查：
    echo 1. 是否已正确安装所有依赖包
    echo 2. 是否已正确配置 .env 文件
    echo 3. 查看日志文件获取详细错误信息
    echo.
)

pause
