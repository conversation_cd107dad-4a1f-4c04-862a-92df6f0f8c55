# -*- coding: utf-8 -*-
"""
Instagram页面元素选择器配置
由于Instagram经常更新页面结构，这里提供多套备用选择器
"""

class InstagramSelectors:
    """Instagram页面选择器集合"""
    
    # 登录页面选择器
    LOGIN_SELECTORS = {
        'username_input': [
            'input[name="username"]',
            'input[aria-label="电话号码、用户名或邮箱"]',
            'input[aria-label="Phone number, username, or email"]',
            'input[placeholder="电话号码、用户名或邮箱"]',
            'input[placeholder="Phone number, username, or email"]'
        ],
        'password_input': [
            'input[name="password"]',
            'input[type="password"]',
            'input[aria-label="密码"]',
            'input[aria-label="Password"]'
        ],
        'login_button': [
            'button[type="submit"]',
            'button._acan._acap._acas._aj1-._ap30',
            'div[role="button"]',
            'button[class*="sqdOP"]',
            'button[class*="_acan"]'
        ]
    }
    
    # 主页面导航选择器
    NAVIGATION_SELECTORS = {
        'dm_button': [
            'a[href="/direct/inbox/"]',
            'a[href*="/direct/"]',
            'a[aria-label="私信"]',
            'a[aria-label="Direct"]',
            'svg[aria-label="私信"]',
            'svg[aria-label="Direct"]',
            'div[class*="x1i10hfl"] a[href*="direct"]'
        ],
        'home_button': [
            'a[href="/"]',
            'a[aria-label="首页"]',
            'a[aria-label="Home"]'
        ]
    }
    
    # 私信页面选择器
    DM_SELECTORS = {
        'conversation_list': [
            'div[role="listbox"]',
            'div[aria-label="对话"]',
            'div[aria-label="Chats"]',
            'div[class*="conversation"]',
            'div[class*="chat"]'
        ],
        'conversation_item': [
            'div[role="button"][tabindex="0"]',
            'a[role="link"]',
            'div[class*="conversation-item"]'
        ],
        'message_container': [
            'div[role="grid"]',
            'div[class*="message-container"]',
            'div[class*="messages"]',
            'div[aria-label="消息"]',
            'div[aria-label="Messages"]'
        ]
    }
    
    # 消息内容选择器
    MESSAGE_SELECTORS = {
        'message_text': [
            'div[dir="auto"]',
            'span[dir="auto"]',
            'div[class*="message-text"]',
            'span[class*="message-text"]',
            'div[role="button"] span',
            'div[data-testid="message-text"]'
        ],
        'message_time': [
            'time',
            'span[title*=":"]',
            'div[class*="timestamp"]',
            'span[class*="timestamp"]',
            'div[class*="time"]'
        ],
        'message_bubble': [
            'div[class*="message"]',
            'div[role="button"]',
            'div[class*="bubble"]'
        ],
        'outgoing_message': [
            'div[class*="outgoing"]',
            'div[class*="sent"]',
            'div[class*="primary"]',
            'div[style*="justify-content: flex-end"]',
            'div[style*="margin-left"]'
        ],
        'incoming_message': [
            'div[class*="incoming"]',
            'div[class*="received"]',
            'div[class*="secondary"]',
            'div[style*="justify-content: flex-start"]',
            'div[style*="margin-right"]'
        ]
    }
    
    # 媒体内容选择器
    MEDIA_SELECTORS = {
        'image': [
            'img[src*="instagram"]',
            'img[class*="message"]',
            'img[role="presentation"]',
            'div[class*="image"] img'
        ],
        'video': [
            'video[src*="instagram"]',
            'video[class*="message"]',
            'div[class*="video"] video'
        ],
        'audio': [
            'audio',
            'div[class*="audio"]',
            'div[aria-label*="语音"]',
            'div[aria-label*="voice"]'
        ]
    }
    
    # 加载更多选择器
    LOAD_MORE_SELECTORS = {
        'load_more_button': [
            'button[class*="load"]',
            'button[class*="more"]',
            'div[role="button"][class*="load"]',
            'div[role="button"][class*="more"]'
        ],
        'scroll_container': [
            'div[class*="scroll"]',
            'div[style*="overflow"]',
            'div[class*="messages"]'
        ]
    }
    
    # 通知和弹窗选择器
    POPUP_SELECTORS = {
        'notification_popup': [
            'div[role="dialog"]',
            'div[class*="modal"]',
            'div[class*="popup"]'
        ],
        'close_button': [
            'button[aria-label="关闭"]',
            'button[aria-label="Close"]',
            'svg[aria-label="关闭"]',
            'svg[aria-label="Close"]'
        ],
        'not_now_button': [
            'button[class*="not-now"]',
            'button[class*="later"]',
            'div[role="button"][class*="secondary"]',
            'button[class*="_acan"][class*="_acap"]'
        ]
    }
    
    @classmethod
    def get_selector(cls, category: str, element: str) -> list:
        """获取指定类别和元素的选择器列表"""
        category_map = {
            'login': cls.LOGIN_SELECTORS,
            'navigation': cls.NAVIGATION_SELECTORS,
            'dm': cls.DM_SELECTORS,
            'message': cls.MESSAGE_SELECTORS,
            'media': cls.MEDIA_SELECTORS,
            'load_more': cls.LOAD_MORE_SELECTORS,
            'popup': cls.POPUP_SELECTORS
        }
        
        if category in category_map and element in category_map[category]:
            return category_map[category][element]
        return []
    
    @classmethod
    def get_all_selectors_for_element(cls, element_name: str) -> list:
        """获取所有类别中指定元素的选择器"""
        all_selectors = []
        
        for category_selectors in [cls.LOGIN_SELECTORS, cls.NAVIGATION_SELECTORS, 
                                 cls.DM_SELECTORS, cls.MESSAGE_SELECTORS,
                                 cls.MEDIA_SELECTORS, cls.LOAD_MORE_SELECTORS,
                                 cls.POPUP_SELECTORS]:
            if element_name in category_selectors:
                all_selectors.extend(category_selectors[element_name])
        
        return list(set(all_selectors))  # 去重
