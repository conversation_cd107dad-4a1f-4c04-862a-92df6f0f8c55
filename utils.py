# -*- coding: utf-8 -*-
"""
工具函数和辅助类
"""

import time
import random
import os
import json
import hashlib
from datetime import datetime
from typing import Optional, List, Dict, Any
import logging
from functools import wraps

from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException, ElementClickInterceptedException
from selenium.webdriver.common.action_chains import ActionChains

from instagram_selectors import InstagramSelectors

logger = logging.getLogger(__name__)

def retry_on_exception(max_retries: int = 3, delay: float = 1.0, backoff: float = 2.0):
    """重试装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            retries = 0
            current_delay = delay
            
            while retries < max_retries:
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    retries += 1
                    if retries >= max_retries:
                        logger.error(f"函数 {func.__name__} 重试 {max_retries} 次后仍然失败: {str(e)}")
                        raise
                    
                    logger.warning(f"函数 {func.__name__} 第 {retries} 次重试，错误: {str(e)}")
                    time.sleep(current_delay)
                    current_delay *= backoff
            
            return None
        return wrapper
    return decorator

def random_delay(min_seconds: float = 1.0, max_seconds: float = 3.0):
    """随机延迟，模拟人类行为"""
    delay = random.uniform(min_seconds, max_seconds)
    time.sleep(delay)

def human_like_scroll(driver, direction: str = "up", pixels: int = None):
    """模拟人类滚动行为"""
    if pixels is None:
        pixels = random.randint(200, 800)
    
    if direction == "up":
        pixels = -pixels
    
    # 分段滚动，更像人类行为
    segments = random.randint(3, 7)
    segment_pixels = pixels // segments
    
    for _ in range(segments):
        driver.execute_script(f"window.scrollBy(0, {segment_pixels});")
        time.sleep(random.uniform(0.1, 0.3))

class ElementFinder:
    """智能元素查找器"""
    
    def __init__(self, driver, wait_timeout: int = 10):
        self.driver = driver
        self.wait = WebDriverWait(driver, wait_timeout)
    
    @retry_on_exception(max_retries=3)
    def find_element_by_selectors(self, selectors: List[str], timeout: int = 10) -> Optional[Any]:
        """使用多个选择器尝试查找元素"""
        for selector in selectors:
            try:
                element = WebDriverWait(self.driver, timeout).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                )
                logger.debug(f"成功使用选择器找到元素: {selector}")
                return element
            except TimeoutException:
                logger.debug(f"选择器未找到元素: {selector}")
                continue
        
        logger.warning(f"所有选择器都未找到元素: {selectors}")
        return None
    
    @retry_on_exception(max_retries=3)
    def find_clickable_element(self, selectors: List[str], timeout: int = 10) -> Optional[Any]:
        """查找可点击的元素"""
        for selector in selectors:
            try:
                element = WebDriverWait(self.driver, timeout).until(
                    EC.element_to_be_clickable((By.CSS_SELECTOR, selector))
                )
                logger.debug(f"成功找到可点击元素: {selector}")
                return element
            except TimeoutException:
                continue
        
        logger.warning(f"未找到可点击元素: {selectors}")
        return None
    
    def safe_click(self, element, use_js: bool = False) -> bool:
        """安全点击元素"""
        try:
            if use_js:
                self.driver.execute_script("arguments[0].click();", element)
            else:
                # 先滚动到元素可见
                self.driver.execute_script("arguments[0].scrollIntoView(true);", element)
                random_delay(0.5, 1.0)
                element.click()
            
            random_delay(1.0, 2.0)
            return True
            
        except ElementClickInterceptedException:
            logger.warning("元素被遮挡，尝试使用JavaScript点击")
            try:
                self.driver.execute_script("arguments[0].click();", element)
                random_delay(1.0, 2.0)
                return True
            except Exception as e:
                logger.error(f"JavaScript点击也失败: {str(e)}")
                return False
        except Exception as e:
            logger.error(f"点击元素失败: {str(e)}")
            return False
    
    def safe_send_keys(self, element, text: str, clear_first: bool = True) -> bool:
        """安全输入文本"""
        try:
            if clear_first:
                element.clear()
                random_delay(0.2, 0.5)
            
            # 模拟人类打字速度
            for char in text:
                element.send_keys(char)
                time.sleep(random.uniform(0.05, 0.15))
            
            random_delay(0.5, 1.0)
            return True
            
        except Exception as e:
            logger.error(f"输入文本失败: {str(e)}")
            return False

class PopupHandler:
    """弹窗处理器"""
    
    def __init__(self, driver):
        self.driver = driver
        self.finder = ElementFinder(driver)
    
    def dismiss_popups(self) -> bool:
        """关闭各种弹窗"""
        popup_dismissed = False
        
        # 尝试关闭通知弹窗
        close_selectors = InstagramSelectors.get_selector('popup', 'close_button')
        close_button = self.finder.find_clickable_element(close_selectors, timeout=3)
        if close_button:
            if self.finder.safe_click(close_button):
                logger.info("关闭了弹窗")
                popup_dismissed = True
        
        # 尝试点击"以后再说"按钮
        not_now_selectors = InstagramSelectors.get_selector('popup', 'not_now_button')
        not_now_button = self.finder.find_clickable_element(not_now_selectors, timeout=3)
        if not_now_button:
            if self.finder.safe_click(not_now_button):
                logger.info("点击了'以后再说'")
                popup_dismissed = True
        
        return popup_dismissed

class DataValidator:
    """数据验证器"""
    
    @staticmethod
    def is_valid_message(content: str) -> bool:
        """验证消息内容是否有效"""
        if not content or not isinstance(content, str):
            return False
        
        content = content.strip()
        if len(content) == 0:
            return False
        
        # 过滤掉一些无意义的内容
        invalid_patterns = [
            "加载中...",
            "Loading...",
            "...",
            "•",
            "○",
            "●"
        ]
        
        for pattern in invalid_patterns:
            if content == pattern:
                return False
        
        return True
    
    @staticmethod
    def is_valid_timestamp(timestamp: str) -> bool:
        """验证时间戳是否有效"""
        if not timestamp:
            return False
        
        try:
            # 尝试解析时间戳
            datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            return True
        except:
            return False
    
    @staticmethod
    def normalize_timestamp(timestamp: str) -> str:
        """标准化时间戳格式"""
        try:
            # 处理各种时间格式
            if 'T' in timestamp:
                dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            else:
                # 尝试其他格式
                dt = datetime.strptime(timestamp, '%Y-%m-%d %H:%M:%S')
            
            return dt.isoformat()
        except:
            # 如果解析失败，返回当前时间
            return datetime.now().isoformat()

class FileManager:
    """文件管理器"""
    
    @staticmethod
    def ensure_directory(directory: str) -> bool:
        """确保目录存在"""
        try:
            os.makedirs(directory, exist_ok=True)
            return True
        except Exception as e:
            logger.error(f"创建目录失败 {directory}: {str(e)}")
            return False
    
    @staticmethod
    def safe_filename(filename: str) -> str:
        """生成安全的文件名"""
        # 移除或替换不安全的字符
        unsafe_chars = '<>:"/\\|?*'
        for char in unsafe_chars:
            filename = filename.replace(char, '_')
        
        # 限制文件名长度
        if len(filename) > 200:
            name, ext = os.path.splitext(filename)
            filename = name[:200-len(ext)] + ext
        
        return filename
    
    @staticmethod
    def generate_unique_filename(base_path: str, filename: str) -> str:
        """生成唯一的文件名"""
        if not os.path.exists(os.path.join(base_path, filename)):
            return filename
        
        name, ext = os.path.splitext(filename)
        counter = 1
        
        while True:
            new_filename = f"{name}_{counter}{ext}"
            if not os.path.exists(os.path.join(base_path, new_filename)):
                return new_filename
            counter += 1
    
    @staticmethod
    def backup_file(filepath: str, backup_dir: str) -> bool:
        """备份文件"""
        try:
            if not os.path.exists(filepath):
                return False
            
            FileManager.ensure_directory(backup_dir)
            
            filename = os.path.basename(filepath)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"{timestamp}_{filename}"
            backup_path = os.path.join(backup_dir, backup_filename)
            
            import shutil
            shutil.copy2(filepath, backup_path)
            
            logger.info(f"文件已备份到: {backup_path}")
            return True
            
        except Exception as e:
            logger.error(f"备份文件失败: {str(e)}")
            return False

def calculate_file_hash(filepath: str) -> str:
    """计算文件哈希值"""
    try:
        with open(filepath, 'rb') as f:
            file_hash = hashlib.md5()
            for chunk in iter(lambda: f.read(4096), b""):
                file_hash.update(chunk)
        return file_hash.hexdigest()
    except:
        return ""

def format_file_size(size_bytes: int) -> str:
    """格式化文件大小"""
    if size_bytes == 0:
        return "0B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f}{size_names[i]}"
