@echo off
chcp 65001 >nul
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                Instagram私信数据提取工具安装脚本              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔍 检查Python环境...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未检测到Python，请先安装Python 3.8+
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python环境检查通过

echo.
echo 📦 安装Python依赖包...
pip install -r requirements.txt

if %errorlevel% neq 0 (
    echo ❌ 依赖包安装失败，请检查网络连接或尝试使用国内镜像源
    echo 尝试运行: pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
    pause
    exit /b 1
)

echo ✅ 依赖包安装完成

echo.
echo 📝 创建配置文件...
if not exist .env (
    copy .env.example .env >nul
    echo ✅ 已创建 .env 配置文件
    echo ⚠️  请编辑 .env 文件，填写您的Instagram登录信息和目标用户名
) else (
    echo ℹ️  .env 配置文件已存在
)

echo.
echo 📁 创建输出目录...
if not exist output mkdir output
if not exist backup mkdir backup
echo ✅ 输出目录创建完成

echo.
echo 🎉 安装完成！
echo.
echo 📋 下一步操作：
echo 1. 编辑 .env 文件，填写您的Instagram登录信息
echo 2. 运行 python main.py 开始使用工具
echo.
echo 💡 提示：
echo - 首次运行可能需要下载Chrome驱动，请保持网络连接
echo - 如果遇到登录问题，可能需要手动处理双因素认证
echo - 详细使用说明请查看 README.md 文件
echo.

pause
