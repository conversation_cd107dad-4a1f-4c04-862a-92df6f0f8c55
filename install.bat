@echo off
chcp 65001 >nul
echo 🔧 Instagram私信提取工具 - 智能安装脚本
echo ================================================

echo.
echo 📋 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未找到Python，请先安装Python 3.8+
    pause
    exit /b 1
)

echo ✅ Python环境检查通过

echo.
echo 📦 开始安装依赖包...
echo.

echo 🔄 方法1: 尝试使用conda安装预编译包...
conda --version >nul 2>&1
if not errorlevel 1 (
    echo ✅ 检测到conda环境，使用conda安装...
    
    echo 📥 安装数据处理包...
    conda install -y pandas numpy matplotlib seaborn plotly pillow openpyxl
    
    echo 📥 安装其他依赖...
    pip install selenium webdriver-manager beautifulsoup4 textblob wordcloud requests python-dotenv tqdm
    
    echo ✅ conda安装完成
    goto :check_installation
)

echo ⚠️  未检测到conda，使用pip安装...
echo.

echo 🔄 方法2: 使用pip安装预编译包...
echo 📥 升级pip...
python -m pip install --upgrade pip

echo 📥 安装核心包（避免编译问题）...
pip install --only-binary=all selenium webdriver-manager beautifulsoup4 requests python-dotenv tqdm

echo 📥 尝试安装数据处理包...
pip install --only-binary=all pandas numpy matplotlib seaborn plotly pillow openpyxl

echo 📥 安装自然语言处理包...
pip install --only-binary=all textblob wordcloud

:check_installation
echo.
echo 🧪 验证安装...
python -c "import selenium, pandas, matplotlib; print('✅ 核心包安装成功')" 2>nul
if errorlevel 1 (
    echo ❌ 安装验证失败，请检查错误信息
    echo.
    echo 💡 建议解决方案：
    echo 1. 安装Microsoft Visual C++ Build Tools
    echo 2. 或使用Anaconda/Miniconda环境
    echo 3. 或使用预编译的Python发行版如WinPython
    pause
    exit /b 1
)

echo.
echo 📁 创建必要目录...
if not exist "output" mkdir output
if not exist "backup" mkdir backup
if not exist "visualizations" mkdir visualizations
if not exist "output\media" mkdir output\media

echo.
echo 📝 创建配置文件...
if not exist ".env" (
    echo # Instagram私信数据提取工具配置文件> .env
    echo.>> .env
    echo # Instagram登录信息>> .env
    echo INSTAGRAM_USERNAME=你的Instagram用户名>> .env
    echo INSTAGRAM_PASSWORD=你的Instagram密码>> .env
    echo TARGET_USERNAME=目标用户名>> .env
    echo.>> .env
    echo # 提取设置>> .env
    echo MAX_MESSAGES=1000>> .env
    echo HEADLESS_MODE=False>> .env
    echo SCROLL_DELAY=2.0>> .env
    echo LOAD_DELAY=3.0>> .env
    echo.>> .env
    echo # 分析设置>> .env
    echo SENTIMENT_ANALYSIS=True>> .env
    echo GENERATE_WORDCLOUD=True>> .env
    echo GENERATE_VISUALIZATIONS=True>> .env
    echo.>> .env
    echo # 输出设置>> .env
    echo OUTPUT_DIR=output>> .env
    echo BACKUP_DIR=backup>> .env
    
    echo ✅ 已创建.env配置文件
    echo ⚠️  请编辑.env文件，填写您的Instagram登录信息
)

echo.
echo 🎉 安装完成！
echo.
echo 📋 下一步操作：
echo 1. 编辑.env文件，填写您的Instagram登录信息
echo 2. 运行 python main.py 开始使用工具
echo 3. 或运行 run.bat 使用图形界面
echo.
echo 💡 如果遇到问题，请查看README.md文档
echo.
pause
