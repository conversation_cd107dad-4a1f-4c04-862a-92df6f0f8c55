# 📱 Instagram私信数据提取和分析工具

![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)
![License](https://img.shields.io/badge/License-Educational-green.svg)
![Platform](https://img.shields.io/badge/Platform-Windows-lightgrey.svg)
![Status](https://img.shields.io/badge/Status-Active-brightgreen.svg)

一个功能强大的Instagram私信数据提取和分析工具，使用Python和Selenium自动化技术来提取、分析和可视化Instagram私信对话数据。

## 🎯 项目概述

该工具提供了完整的Instagram私信数据处理解决方案，从数据提取到深度分析，再到可视化展示，为用户提供全面的对话数据洞察。

## ⚠️ 重要声明

**请务必遵守以下原则：**
- 仅用于个人学习和研究目的
- 遵守Instagram的使用条款和服务协议
- 尊重他人隐私，不要未经授权提取他人数据
- 遵守当地法律法规关于数据隐私的规定

## ✨ 主要特性

### 🤖 智能自动化
- ✅ 自动登录Instagram并处理双因素认证
- ✅ 智能导航到指定用户的私信对话
- ✅ 模拟人类行为，避免被检测
- ✅ 智能处理页面变化和弹窗

### 📊 全面数据分析
- ✅ 基础统计信息（消息数量、时间范围等）
- ✅ 时间模式分析（按日、周、月、小时）
- ✅ 情感分析（正面、负面、中性倾向）
- ✅ 词频统计和词云生成
- ✅ 对话模式识别

### 🎨 丰富可视化
- ✅ 消息时间趋势图
- ✅ 每日活跃度分布
- ✅ 小时级活跃度热力图
- ✅ 情感分析图表
- ✅ 交互式数据展示

### 🛡️ 安全可靠
- ✅ 本地数据处理，保护隐私
- ✅ 完善的错误处理和重试机制
- ✅ 数据备份和恢复功能
- ✅ 遵守使用条款和法律法规

## 🚀 快速开始

### 1. 环境要求
- Python 3.8 或更高版本
- Chrome浏览器
- Windows操作系统（推荐）

### 2. 安装依赖
```bash
# 方法1: 使用安装脚本（推荐）
install.bat

# 方法2: 手动安装
pip install -r requirements.txt
```

### 3. 配置环境
```bash
# 复制配置文件模板
copy .env.example .env

# 编辑.env文件，填写以下信息：
INSTAGRAM_USERNAME=你的Instagram用户名
INSTAGRAM_PASSWORD=你的Instagram密码
TARGET_USERNAME=目标用户名
```

### 4. 运行程序
```bash
# 方法1: 使用运行脚本
run.bat

# 方法2: 直接运行
python main.py
```

## 📋 使用说明

### 交互式菜单
程序启动后会显示交互式菜单：
```
1. 提取Instagram私信数据
2. 分析已有数据
3. 完整流程（提取+分析）
4. 查看配置信息
5. 退出程序
```

### 命令行参数
```bash
# 提取指定用户的数据
python main.py --extract --target 用户名

# 分析已有数据
python main.py --analyze

# 完整流程
python main.py --all --target 用户名

# 查看帮助
python main.py --help
```

## 📁 项目结构

```
instagram-dm-extractor/
├── 📄 main.py                    # 主程序入口
├── 🤖 instagram_extractor.py     # Instagram数据提取器
├── 📊 data_analyzer.py          # 数据分析器
├── ⚙️ config.py                 # 配置管理
├── 🔧 utils.py                  # 工具函数
├── 📋 instagram_selectors.py    # 页面选择器配置
├── 📦 requirements.txt          # 依赖包列表
├── 📝 .env.example             # 配置文件模板
├── 🚫 .gitignore               # Git忽略文件
├── 🔧 install.bat              # 安装脚本
├── ▶️ run.bat                  # 运行脚本
├── 🎯 demo.py                  # 演示脚本
└── 📚 README.md                # 项目说明
```

## 🔧 配置选项

### 基础配置（.env文件）
```env
# Instagram登录信息
INSTAGRAM_USERNAME=你的用户名
INSTAGRAM_PASSWORD=你的密码
TARGET_USERNAME=目标用户名

# 提取设置
MAX_MESSAGES=1000              # 最大提取消息数
HEADLESS_MODE=False           # 无头模式
SCROLL_DELAY=2.0             # 滚动延迟（秒）
LOAD_DELAY=3.0               # 页面加载延迟（秒）

# 分析设置
SENTIMENT_ANALYSIS=True       # 启用情感分析
GENERATE_WORDCLOUD=True      # 生成词云
GENERATE_VISUALIZATIONS=True  # 生成可视化图表

# 输出设置
OUTPUT_DIR=output            # 输出目录
BACKUP_DIR=backup           # 备份目录
```

## 📊 输出文件

### 数据文件
- `instagram_dm_用户名_时间戳.json` - 原始对话数据
- `analysis_report_时间戳.json` - 分析报告数据

### 可视化图表
- `visualizations/message_timeline.png` - 消息时间趋势
- `visualizations/daily_distribution.png` - 每日消息分布
- `visualizations/hourly_heatmap.png` - 小时活跃度热力图
- `visualizations/sender_distribution.png` - 发送者比例
- `visualizations/sentiment_analysis.png` - 情感分析图表
- `visualizations/wordcloud.png` - 词云图

### 媒体文件
- `media/` - 下载的图片、视频等媒体文件

## 🛠️ 技术架构

### 核心技术栈
- **Python 3.8+** - 主要编程语言
- **Selenium** - 浏览器自动化框架
- **BeautifulSoup** - HTML解析
- **Pandas** - 数据处理和分析
- **Matplotlib/Seaborn** - 数据可视化
- **Plotly** - 交互式图表
- **TextBlob** - 自然语言处理和情感分析

### 设计特点
- 🏗️ **模块化设计** - 功能分离，便于维护和扩展
- ⚙️ **配置驱动** - 通过配置文件管理参数
- 🔄 **重试机制** - 自动重试失败的操作
- 🎭 **人类行为模拟** - 随机延迟和自然滚动
- 🛡️ **错误恢复** - 完善的异常处理机制

## 🔍 常见问题

### Q: 程序运行时出现登录失败？
A: 请检查：
- Instagram用户名和密码是否正确
- 是否启用了双因素认证（程序会提示手动完成）
- 网络连接是否稳定
- 是否被Instagram临时限制

### Q: 提取数据时页面加载缓慢？
A: 可以调整配置：
- 增加 `LOAD_DELAY` 和 `SCROLL_DELAY` 的值
- 检查网络连接速度
- 尝试在网络较好的时间段运行

### Q: 分析结果不准确？
A: 可能的原因：
- 数据量较小，统计结果可能不够准确
- 情感分析基于英文模型，中文效果可能有限
- 可以手动检查部分数据验证结果

## 🤝 贡献指南

欢迎贡献代码和建议！请遵循以下步骤：

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目仅供学习和研究使用。使用者需要：
- 遵守Instagram的使用条款
- 遵守当地法律法规
- 尊重他人隐私权
- 承担使用风险和责任

## 🙏 致谢

感谢以下开源项目的支持：
- [Selenium](https://selenium-python.readthedocs.io/) - 浏览器自动化
- [Pandas](https://pandas.pydata.org/) - 数据处理
- [Matplotlib](https://matplotlib.org/) - 数据可视化
- [TextBlob](https://textblob.readthedocs.io/) - 自然语言处理

---

⭐ 如果这个项目对你有帮助，请给它一个星标！

📧 如有问题或建议，欢迎提交Issue或联系开发者。
