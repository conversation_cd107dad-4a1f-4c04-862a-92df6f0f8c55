# 📱 Instagram 私信数据提取和分析工具

![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)
![License](https://img.shields.io/badge/License-Educational-green.svg)
![Platform](https://img.shields.io/badge/Platform-Windows-lightgrey.svg)
![Status](https://img.shields.io/badge/Status-Active-brightgreen.svg)

这是一个功能强大的 Instagram 私信数据提取和分析工具，使用 Python 和 Selenium 自动化技术来提取、分析和可视化 Instagram 私信对话数据。

## 🎯 项目概述

该工具提供了完整的 Instagram 私信数据处理解决方案，从数据提取到深度分析，再到可视化展示，为用户提供全面的对话数据洞察。

## ⚠️ 重要声明

**请务必遵守以下原则：**

- 仅用于个人学习和研究目的
- 遵守 Instagram 的使用条款和服务协议
- 尊重他人隐私，不要未经授权提取他人数据
- 遵守当地法律法规关于数据隐私的规定

## 🚀 功能特性

### 数据提取功能

- ✅ 自动登录 Instagram 账户
- ✅ 提取指定用户的私信对话历史
- ✅ 支持文本消息、图片、视频等多种消息类型
- ✅ 自动下载对话中的媒体文件
- ✅ 智能滚动加载历史消息
- ✅ 数据去重和验证

### 数据分析功能

- ✅ 基础统计信息分析
- ✅ 时间模式分析（按日、周、月、小时）
- ✅ 情感分析（正面、负面、中性）
- ✅ 对话模式分析（响应时间、连续消息等）
- ✅ 词频统计和词云生成
- ✅ 多维度数据可视化

### 可视化图表

- 📊 消息数量时间趋势图
- 📊 每日/每周消息分布图
- 📊 小时活跃度热力图
- 📊 发送者消息比例饼图
- 📊 消息类型分布图
- 📊 情感分析图表
- 📊 词云图

## 📋 系统要求

- Python 3.8+
- Chrome 浏览器
- Windows/macOS/Linux

## 🛠️ 安装步骤

### 1. 克隆或下载项目

```bash
git clone <repository-url>
cd instagram-dm-extractor
```

### 2. 安装 Python 依赖

```bash
pip install -r requirements.txt
```

### 3. 配置环境变量

复制 `.env.example` 文件为 `.env` 并填写配置：

```bash
cp .env.example .env
```

编辑 `.env` 文件：

```env
# Instagram登录信息
INSTAGRAM_USERNAME=你的Instagram用户名
INSTAGRAM_PASSWORD=你的Instagram密码

# 目标用户
TARGET_USERNAME=要提取对话的用户名

# 可选配置
MAX_MESSAGES=1000
HEADLESS_MODE=False
SENTIMENT_ANALYSIS=True
GENERATE_WORDCLOUD=True
```

## 🎯 使用方法

### 方法 1: 交互式菜单（推荐）

```bash
python main.py
```

### 方法 2: 命令行参数

```bash
# 仅提取数据
python main.py --extract --target 目标用户名

# 仅分析数据
python main.py --analyze

# 完整流程
python main.py --all --target 目标用户名
```

## 📁 输出文件结构

```
output/
├── instagram_dm_用户名_时间戳.json     # 原始对话数据
├── analysis_report_时间戳.json        # 分析报告
├── media/                            # 下载的媒体文件
│   ├── 用户名_0001_日期.jpg
│   └── ...
└── visualizations/                   # 可视化图表
    ├── message_timeline.png
    ├── daily_distribution.png
    ├── hourly_heatmap.png
    ├── sender_distribution.png
    ├── message_types.png
    ├── sentiment_analysis.png
    └── wordcloud.png
```

## 📊 数据格式

### 消息数据结构

```json
{
  "id": "消息唯一标识",
  "sender": "发送者用户名",
  "content": "消息内容",
  "timestamp": "2024-01-01T12:00:00",
  "message_type": "text|image|video",
  "media_url": "媒体文件URL",
  "media_path": "本地媒体文件路径",
  "is_outgoing": true
}
```

### 分析报告结构

```json
{
  "basic_statistics": {
    "total_messages": 1000,
    "date_range": {"start": "2024-01-01", "end": "2024-12-31"},
    "message_counts": {"用户1": 600, "用户2": 400}
  },
  "temporal_patterns": {
    "daily_message_counts": {...},
    "hourly_distribution": {...},
    "most_active_hour": 20
  },
  "sentiment_analysis": {
    "sentiment_distribution": {"positive": 400, "neutral": 500, "negative": 100}
  }
}
```

## ⚙️ 高级配置

### 浏览器配置

- `HEADLESS_MODE`: 设置为 `True` 可在后台运行（无界面）
- `CHROME_DRIVER_PATH`: 手动指定 Chrome 驱动路径

### 提取配置

- `MAX_MESSAGES`: 最大提取消息数量
- `SCROLL_DELAY`: 滚动延迟时间（秒）
- `LOAD_DELAY`: 页面加载延迟时间（秒）

### 分析配置

- `SENTIMENT_ANALYSIS`: 是否进行情感分析
- `GENERATE_WORDCLOUD`: 是否生成词云图

## 🔧 故障排除

### 常见问题

1. **登录失败**

   - 检查用户名和密码是否正确
   - 确认账户没有被限制
   - 可能需要手动处理双因素认证

2. **Chrome 驱动问题**

   - 确保 Chrome 浏览器已安装
   - 程序会自动下载匹配的驱动

3. **数据提取不完整**

   - 增加 `SCROLL_DELAY` 和 `LOAD_DELAY` 时间
   - 检查网络连接稳定性

4. **分析报告生成失败**
   - 检查数据文件是否完整
   - 确认所有依赖库已正确安装

### 日志文件

程序运行时会生成详细的日志文件：

- `instagram_extractor.log`: 提取过程日志
- `instagram_dm_tool.log`: 主程序日志

## 📝 开发说明

### 项目结构

```
├── main.py                 # 主程序入口
├── instagram_extractor.py  # Instagram数据提取器
├── data_analyzer.py        # 数据分析器
├── config.py              # 配置管理
├── requirements.txt       # Python依赖
├── .env.example          # 环境变量示例
└── README.md             # 说明文档
```

### 扩展开发

- 可以修改 `config.py` 中的选择器来适应 Instagram 页面变化
- 可以在 `data_analyzer.py` 中添加新的分析功能
- 可以扩展支持更多的消息类型和媒体格式

## 📄 许可证

本项目仅供学习和研究使用。使用者需要：

- 遵守 Instagram 的使用条款
- 遵守当地法律法规
- 尊重他人隐私权

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目！

## ⚠️ 免责声明

本工具仅用于教育和研究目的。使用者需要自行承担使用本工具的风险和责任。开发者不对因使用本工具而产生的任何后果负责。
