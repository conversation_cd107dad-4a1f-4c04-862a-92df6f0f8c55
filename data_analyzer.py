# -*- coding: utf-8 -*-
"""
Instagram私信数据分析器
对提取的对话数据进行统计分析和可视化
"""

import json
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from datetime import datetime, timedelta
from collections import Counter
import re
from typing import Dict, List, Tuple, Optional
import logging
from textblob import TextBlob
from wordcloud import WordCloud
import os

from config import Config

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

logger = logging.getLogger(__name__)

class DataAnalyzer:
    """Instagram私信数据分析器"""
    
    def __init__(self, data_file: str):
        self.config = Config()
        self.data_file = data_file
        self.conversation_data: Optional[Dict] = None
        self.df: Optional[pd.DataFrame] = None
        self.load_data()
    
    def load_data(self) -> None:
        """加载对话数据"""
        try:
            with open(self.data_file, 'r', encoding='utf-8') as f:
                self.conversation_data = json.load(f)
            
            # 转换为DataFrame
            messages = self.conversation_data['messages']
            self.df = pd.DataFrame(messages)
            
            # 数据预处理
            self.df['timestamp'] = pd.to_datetime(self.df['timestamp'])
            self.df['date'] = self.df['timestamp'].dt.date
            self.df['hour'] = self.df['timestamp'].dt.hour
            self.df['weekday'] = self.df['timestamp'].dt.day_name()
            self.df['month'] = self.df['timestamp'].dt.month
            self.df['year'] = self.df['timestamp'].dt.year
            
            # 计算消息长度
            self.df['message_length'] = self.df['content'].str.len()
            
            logger.info(f"成功加载 {len(self.df)} 条消息数据")
            
        except Exception as e:
            logger.error(f"加载数据失败: {str(e)}")
            raise
    
    def generate_basic_statistics(self) -> Dict:
        """生成基础统计信息"""
        if self.df is None or self.conversation_data is None:
            return {}

        stats = {
            'total_messages': len(self.df),
            'date_range': {
                'start': self.df['timestamp'].min().strftime('%Y-%m-%d'),
                'end': self.df['timestamp'].max().strftime('%Y-%m-%d')
            },
            'participants': {
                'user1': self.conversation_data.get('participant1', 'Unknown'),
                'user2': self.conversation_data.get('participant2', 'Unknown')
            },
            'message_counts': self.df['sender'].value_counts().to_dict(),
            'message_types': self.df['message_type'].value_counts().to_dict(),
            'average_message_length': self.df['message_length'].mean(),
            'total_days': (self.df['timestamp'].max() - self.df['timestamp'].min()).days,
            'messages_per_day': len(self.df) / max(1, (self.df['timestamp'].max() - self.df['timestamp'].min()).days)
        }
        
        return stats
    
    def analyze_temporal_patterns(self) -> Dict:
        """分析时间模式"""
        if self.df is None:
            return {}

        temporal_analysis = {}

        # 按日期统计消息数量
        daily_counts = self.df.groupby('date').size()
        temporal_analysis['daily_message_counts'] = daily_counts.to_dict()

        # 按小时统计
        hourly_counts = self.df.groupby('hour').size()
        temporal_analysis['hourly_distribution'] = hourly_counts.to_dict()

        # 按星期统计
        weekday_counts = self.df.groupby('weekday').size()
        temporal_analysis['weekday_distribution'] = weekday_counts.to_dict()

        # 按月份统计
        monthly_counts = self.df.groupby('month').size()
        temporal_analysis['monthly_distribution'] = monthly_counts.to_dict()
        
        # 最活跃的时间段
        temporal_analysis['most_active_hour'] = hourly_counts.idxmax()
        temporal_analysis['most_active_weekday'] = weekday_counts.idxmax()
        
        return temporal_analysis
    
    def perform_sentiment_analysis(self) -> Dict:
        """执行情感分析"""
        if not self.config.SENTIMENT_ANALYSIS or self.df is None:
            return {}

        sentiments = []
        polarities = []
        subjectivities = []

        for content in self.df['content']:
            try:
                blob = TextBlob(content)
                polarity = blob.sentiment.polarity
                subjectivity = blob.sentiment.subjectivity
                
                polarities.append(polarity)
                subjectivities.append(subjectivity)
                
                # 分类情感
                if polarity > 0.1:
                    sentiment = 'positive'
                elif polarity < -0.1:
                    sentiment = 'negative'
                else:
                    sentiment = 'neutral'
                
                sentiments.append(sentiment)
                
            except Exception:
                sentiments.append('neutral')
                polarities.append(0)
                subjectivities.append(0)
        
        self.df['sentiment'] = sentiments
        self.df['polarity'] = polarities
        self.df['subjectivity'] = subjectivities
        
        sentiment_analysis = {
            'sentiment_distribution': pd.Series(sentiments).value_counts().to_dict(),
            'average_polarity': np.mean(polarities),
            'average_subjectivity': np.mean(subjectivities),
            'sentiment_by_sender': self.df.groupby('sender')['sentiment'].value_counts().to_dict()
        }
        
        return sentiment_analysis
    
    def analyze_conversation_patterns(self) -> Dict:
        """分析对话模式"""
        patterns = {}
        
        # 响应时间分析（简化版）
        self.df_sorted = self.df.sort_values('timestamp')
        response_times = []
        
        for i in range(1, len(self.df_sorted)):
            current_msg = self.df_sorted.iloc[i]
            prev_msg = self.df_sorted.iloc[i-1]
            
            # 如果是不同发送者之间的对话
            if current_msg['sender'] != prev_msg['sender']:
                time_diff = (current_msg['timestamp'] - prev_msg['timestamp']).total_seconds() / 60  # 分钟
                if time_diff < 1440:  # 小于24小时
                    response_times.append(time_diff)
        
        if response_times:
            patterns['average_response_time_minutes'] = np.mean(response_times)
            patterns['median_response_time_minutes'] = np.median(response_times)
        
        # 连续消息分析
        consecutive_messages = self.df_sorted.groupby((self.df_sorted['sender'] != self.df_sorted['sender'].shift()).cumsum()).size()
        patterns['average_consecutive_messages'] = consecutive_messages.mean()
        patterns['max_consecutive_messages'] = consecutive_messages.max()
        
        return patterns
    
    def generate_word_frequency(self) -> Dict:
        """生成词频统计"""
        # 合并所有文本
        all_text = ' '.join(self.df['content'].astype(str))
        
        # 简单的词频统计（可以根据需要改进）
        words = re.findall(r'\b\w+\b', all_text.lower())
        word_freq = Counter(words)
        
        # 过滤常见停用词
        stop_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'must', 'can', 'i', 'you', 'he', 'she', 'it', 'we', 'they', 'me', 'him', 'her', 'us', 'them', 'my', 'your', 'his', 'her', 'its', 'our', 'their'}
        
        filtered_word_freq = {word: count for word, count in word_freq.items() 
                             if word not in stop_words and len(word) > 2}
        
        # 返回前50个最常用词
        top_words = dict(sorted(filtered_word_freq.items(), key=lambda x: x[1], reverse=True)[:50])
        
        return top_words

    def create_visualizations(self) -> None:
        """创建可视化图表"""
        logger.info("正在生成可视化图表...")

        # 创建输出目录
        viz_dir = os.path.join(self.config.OUTPUT_DIR, 'visualizations')
        os.makedirs(viz_dir, exist_ok=True)

        # 1. 消息数量时间趋势图
        self._plot_message_timeline(viz_dir)

        # 2. 每日消息分布
        self._plot_daily_distribution(viz_dir)

        # 3. 小时分布热力图
        self._plot_hourly_heatmap(viz_dir)

        # 4. 发送者消息比例
        self._plot_sender_distribution(viz_dir)

        # 5. 消息类型分布
        self._plot_message_types(viz_dir)

        # 6. 情感分析图表
        if 'sentiment' in self.df.columns:
            self._plot_sentiment_analysis(viz_dir)

        # 7. 词云图
        if self.config.GENERATE_WORDCLOUD:
            self._generate_wordcloud(viz_dir)

        logger.info(f"可视化图表已保存到: {viz_dir}")

    def _plot_message_timeline(self, output_dir: str) -> None:
        """绘制消息时间线图"""
        daily_counts = self.df.groupby('date').size()

        fig, ax = plt.subplots(figsize=(12, 6))
        daily_counts.plot(kind='line', ax=ax, marker='o')
        ax.set_title('消息数量时间趋势', fontsize=16)
        ax.set_xlabel('日期', fontsize=12)
        ax.set_ylabel('消息数量', fontsize=12)
        ax.grid(True, alpha=0.3)
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'message_timeline.png'), dpi=300, bbox_inches='tight')
        plt.close()

    def _plot_daily_distribution(self, output_dir: str) -> None:
        """绘制每日消息分布图"""
        weekday_counts = self.df.groupby('weekday').size()
        weekday_order = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
        weekday_counts = weekday_counts.reindex(weekday_order)

        fig, ax = plt.subplots(figsize=(10, 6))
        weekday_counts.plot(kind='bar', ax=ax, color='skyblue')
        ax.set_title('星期消息分布', fontsize=16)
        ax.set_xlabel('星期', fontsize=12)
        ax.set_ylabel('消息数量', fontsize=12)
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'daily_distribution.png'), dpi=300, bbox_inches='tight')
        plt.close()

    def _plot_hourly_heatmap(self, output_dir: str) -> None:
        """绘制小时分布热力图"""
        hourly_data = self.df.groupby(['weekday', 'hour']).size().unstack(fill_value=0)
        weekday_order = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
        hourly_data = hourly_data.reindex(weekday_order)

        fig, ax = plt.subplots(figsize=(12, 6))
        sns.heatmap(hourly_data, annot=False, cmap='YlOrRd', ax=ax)
        ax.set_title('消息活跃度热力图 (星期 vs 小时)', fontsize=16)
        ax.set_xlabel('小时', fontsize=12)
        ax.set_ylabel('星期', fontsize=12)
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'hourly_heatmap.png'), dpi=300, bbox_inches='tight')
        plt.close()

    def _plot_sender_distribution(self, output_dir: str) -> None:
        """绘制发送者消息分布饼图"""
        sender_counts = self.df['sender'].value_counts()

        fig, ax = plt.subplots(figsize=(8, 8))
        ax.pie(sender_counts.values, labels=sender_counts.index, autopct='%1.1f%%', startangle=90)
        ax.set_title('发送者消息比例', fontsize=16)
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'sender_distribution.png'), dpi=300, bbox_inches='tight')
        plt.close()

    def _plot_message_types(self, output_dir: str) -> None:
        """绘制消息类型分布图"""
        type_counts = self.df['message_type'].value_counts()

        fig, ax = plt.subplots(figsize=(8, 6))
        type_counts.plot(kind='bar', ax=ax, color='lightcoral')
        ax.set_title('消息类型分布', fontsize=16)
        ax.set_xlabel('消息类型', fontsize=12)
        ax.set_ylabel('数量', fontsize=12)
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'message_types.png'), dpi=300, bbox_inches='tight')
        plt.close()

    def _plot_sentiment_analysis(self, output_dir: str) -> None:
        """绘制情感分析图表"""
        sentiment_counts = self.df['sentiment'].value_counts()

        # 情感分布饼图
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

        ax1.pie(sentiment_counts.values, labels=sentiment_counts.index, autopct='%1.1f%%', startangle=90)
        ax1.set_title('情感分布', fontsize=14)

        # 情感时间趋势
        sentiment_timeline = self.df.groupby(['date', 'sentiment']).size().unstack(fill_value=0)
        sentiment_timeline.plot(kind='area', stacked=True, ax=ax2, alpha=0.7)
        ax2.set_title('情感时间趋势', fontsize=14)
        ax2.set_xlabel('日期', fontsize=12)
        ax2.set_ylabel('消息数量', fontsize=12)
        ax2.legend(title='情感')

        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'sentiment_analysis.png'), dpi=300, bbox_inches='tight')
        plt.close()

    def _generate_wordcloud(self, output_dir: str) -> None:
        """生成词云图"""
        try:
            all_text = ' '.join(self.df['content'].astype(str))

            wordcloud = WordCloud(
                width=800,
                height=400,
                background_color='white',
                max_words=100,
                colormap='viridis'
            ).generate(all_text)

            fig, ax = plt.subplots(figsize=(10, 5))
            ax.imshow(wordcloud, interpolation='bilinear')
            ax.axis('off')
            ax.set_title('词云图', fontsize=16)
            plt.tight_layout()
            plt.savefig(os.path.join(output_dir, 'wordcloud.png'), dpi=300, bbox_inches='tight')
            plt.close()

        except Exception as e:
            logger.warning(f"生成词云图失败: {str(e)}")

    def generate_comprehensive_report(self) -> str:
        """生成综合分析报告"""
        logger.info("正在生成综合分析报告...")

        # 收集所有分析结果
        basic_stats = self.generate_basic_statistics()
        temporal_patterns = self.analyze_temporal_patterns()
        sentiment_analysis = self.perform_sentiment_analysis()
        conversation_patterns = self.analyze_conversation_patterns()
        word_frequency = self.generate_word_frequency()

        # 生成可视化
        self.create_visualizations()

        # 创建报告
        report = {
            'basic_statistics': basic_stats,
            'temporal_patterns': temporal_patterns,
            'sentiment_analysis': sentiment_analysis,
            'conversation_patterns': conversation_patterns,
            'word_frequency': word_frequency,
            'analysis_date': datetime.now().isoformat()
        }

        # 保存报告
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_filename = f"analysis_report_{timestamp}.json"
        report_filepath = os.path.join(self.config.OUTPUT_DIR, report_filename)

        with open(report_filepath, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)

        logger.info(f"综合分析报告已保存到: {report_filepath}")
        return report_filepath
