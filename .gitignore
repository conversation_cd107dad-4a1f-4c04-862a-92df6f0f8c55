# Instagram DM Extractor - GitIgnore配置

# 环境变量和敏感信息
.env
*.env
.env.local
.env.production

# Python相关
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# 虚拟环境
venv/
env/
ENV/
env.bak/
venv.bak/
.venv/

# IDE和编辑器
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# 日志文件
*.log
logs/
instagram_extractor.log
instagram_dm_tool.log

# 输出文件和数据
output/
backup/
data/
downloads/
media/
*.json
*.csv
*.xlsx

# 可视化图表（可选择性忽略）
visualizations/
*.png
*.jpg
*.jpeg
*.gif
*.svg

# Chrome驱动和浏览器相关
chromedriver*
geckodriver*
*.exe

# 临时文件
temp/
tmp/
*.tmp
*.temp

# 系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 压缩文件
*.zip
*.rar
*.7z
*.tar.gz

# 测试覆盖率
htmlcov/
.coverage
.coverage.*
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# 其他
*.bak
*.orig
*.rej
