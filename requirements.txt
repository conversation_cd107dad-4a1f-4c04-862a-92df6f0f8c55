# Instagram私信数据提取和分析工具 - 依赖包列表
# 使用预编译的二进制包，避免编译问题

# 浏览器自动化
selenium>=4.15.0
webdriver-manager>=4.0.0

# 网页解析
beautifulsoup4>=4.12.0
requests>=2.31.0

# 数据处理和分析 (使用conda-forge预编译版本)
pandas>=2.0.0
numpy>=1.24.0

# 数据可视化
matplotlib>=3.7.0
seaborn>=0.12.0
plotly>=5.15.0

# 自然语言处理
textblob>=0.17.0
wordcloud>=1.9.0

# 用户界面和进度显示
tqdm>=4.66.0

# 环境变量管理
python-dotenv>=1.0.0

# 图像处理
Pillow>=10.0.0

# 文件格式支持
openpyxl>=3.1.0
