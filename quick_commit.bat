@echo off
chcp 65001 >nul
echo 🚀 快速Git提交脚本
echo.

REM 检查是否在Git仓库中
git status >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 当前目录不是Git仓库
    echo 请先运行 git_setup.bat 初始化仓库
    pause
    exit /b 1
)

echo 📋 当前Git状态:
git status --short

echo.
set /p COMMIT_MSG="请输入提交信息 (或按回车使用默认信息): "

if "%COMMIT_MSG%"=="" (
    set COMMIT_MSG=📝 更新代码和文档
)

echo.
echo 📁 添加所有更改...
git add .

echo 💬 创建提交...
git commit -m "%COMMIT_MSG%"

echo 📤 推送到GitLab...
git push origin main

if %errorlevel% equ 0 (
    echo.
    echo ✅ 成功推送到GitLab！
) else (
    echo.
    echo ❌ 推送失败，请检查网络连接和权限
)

echo.
pause
