# -*- coding: utf-8 -*-
"""
Instagram私信数据提取工具演示脚本
简化版本，用于快速测试和演示
"""

import os
import sys
import logging
from datetime import datetime

# 设置简单的日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def print_demo_banner():
    """打印演示横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                Instagram私信数据提取工具 - 演示版             ║
║                                                              ║
║  这是一个简化的演示版本，展示工具的基本功能                   ║
║  完整版本请使用 main.py                                      ║
╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_basic_requirements():
    """检查基本环境要求"""
    print("🔍 检查基本环境...")
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ Python版本过低，需要Python 3.8+")
        return False
    
    # 检查必要的库
    required_packages = ['selenium', 'pandas', 'matplotlib', 'requests']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ 缺少必要的包: {', '.join(missing_packages)}")
        print("请运行: pip install -r requirements.txt")
        return False
    
    print("✅ 基本环境检查通过")
    return True

def create_demo_config():
    """创建演示配置"""
    print("📝 创建演示配置...")
    
    if not os.path.exists('.env'):
        print("⚠️  未找到.env配置文件")
        print("请按照以下步骤配置:")
        print("1. 复制 .env.example 为 .env")
        print("2. 编辑 .env 文件，填写您的Instagram登录信息")
        print("3. 设置目标用户名")
        return False
    
    print("✅ 配置文件存在")
    return True

def demo_data_extraction():
    """演示数据提取功能"""
    print("\n🚀 演示数据提取功能...")
    
    try:
        from instagram_extractor import InstagramExtractor
        
        print("✅ Instagram提取器模块加载成功")
        
        # 创建提取器实例
        extractor = InstagramExtractor()
        print("✅ 提取器实例创建成功")
        
        print("ℹ️  实际提取需要:")
        print("   - 有效的Instagram登录凭据")
        print("   - 目标用户名")
        print("   - 稳定的网络连接")
        print("   - Chrome浏览器")
        
        return True
        
    except Exception as e:
        print(f"❌ 提取器模块加载失败: {str(e)}")
        return False

def demo_data_analysis():
    """演示数据分析功能"""
    print("\n📊 演示数据分析功能...")
    
    try:
        from data_analyzer import DataAnalyzer
        
        print("✅ 数据分析器模块加载成功")
        
        print("ℹ️  分析功能包括:")
        print("   - 基础统计信息")
        print("   - 时间模式分析")
        print("   - 情感分析")
        print("   - 词频统计")
        print("   - 可视化图表生成")
        
        return True
        
    except Exception as e:
        print(f"❌ 分析器模块加载失败: {str(e)}")
        return False

def show_demo_features():
    """展示演示功能"""
    print("\n🎯 工具主要功能:")
    print("1. 📱 Instagram私信数据提取")
    print("   - 自动登录Instagram")
    print("   - 提取指定用户的对话历史")
    print("   - 下载对话中的媒体文件")
    print("   - 智能处理页面变化")
    
    print("\n2. 📊 数据分析和可视化")
    print("   - 消息统计分析")
    print("   - 时间模式识别")
    print("   - 情感分析")
    print("   - 词云生成")
    print("   - 多种图表展示")
    
    print("\n3. 🛡️ 安全和隐私")
    print("   - 本地数据处理")
    print("   - 遵守使用条款")
    print("   - 数据备份机制")
    print("   - 错误恢复功能")

def show_usage_instructions():
    """显示使用说明"""
    print("\n📋 使用说明:")
    print("1. 安装依赖: pip install -r requirements.txt")
    print("2. 配置环境: 复制 .env.example 为 .env 并填写配置")
    print("3. 运行工具: python main.py")
    print("4. 选择操作: 提取数据 -> 分析数据 -> 查看结果")
    
    print("\n⚠️  重要提醒:")
    print("- 仅用于个人学习和研究")
    print("- 遵守Instagram使用条款")
    print("- 尊重他人隐私")
    print("- 遵守当地法律法规")

def main():
    """演示主函数"""
    print_demo_banner()
    
    # 检查环境
    if not check_basic_requirements():
        return
    
    # 检查配置
    if not create_demo_config():
        return
    
    # 演示功能模块
    demo_data_extraction()
    demo_data_analysis()
    
    # 展示功能特性
    show_demo_features()
    
    # 显示使用说明
    show_usage_instructions()
    
    print("\n🎉 演示完成!")
    print("要开始实际使用，请运行: python main.py")
    
    # 询问是否运行完整版本
    choice = input("\n是否现在运行完整版本? (y/n): ").strip().lower()
    if choice in ['y', 'yes', '是']:
        try:
            import main
            main.main()
        except Exception as e:
            print(f"❌ 运行完整版本失败: {str(e)}")
            print("请直接运行: python main.py")

if __name__ == "__main__":
    main()
