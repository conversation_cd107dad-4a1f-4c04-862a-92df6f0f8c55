@echo off
chcp 65001 >nul
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                GitLab仓库创建和提交脚本                      ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔧 Git仓库初始化和提交脚本
echo.

REM 检查Git是否安装
git --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未检测到Git，请先安装Git
    echo 下载地址: https://git-scm.com/download/win
    pause
    exit /b 1
)

echo ✅ Git环境检查通过
echo.

REM 获取用户输入
set /p GITLAB_URL="请输入GitLab仓库URL (例: https://gitlab.com/username/**********************.git): "
set /p USER_NAME="请输入您的Git用户名: "
set /p USER_EMAIL="请输入您的Git邮箱: "

echo.
echo 📝 配置Git用户信息...
git config --global user.name "%USER_NAME%"
git config --global user.email "%USER_EMAIL%"

echo ✅ Git用户信息配置完成
echo.

echo 🚀 初始化Git仓库...
git init

echo 📁 添加文件到Git...
git add .

echo 💬 创建初始提交...
git commit -m "🎉 初始提交: Instagram私信数据提取和分析工具

✨ 功能特性:
- Instagram私信数据自动提取
- 智能浏览器自动化
- 多维度数据分析
- 丰富的可视化图表
- 情感分析和词频统计
- 媒体文件自动下载

🛠️ 技术栈:
- Python 3.8+
- Selenium WebDriver
- Pandas & Matplotlib
- TextBlob NLP
- BeautifulSoup

📋 项目结构:
- 模块化设计
- 配置驱动
- 错误处理和重试机制
- 人类行为模拟

⚠️ 使用说明:
- 仅用于个人学习和研究
- 遵守Instagram使用条款
- 尊重隐私和法律法规"

echo 🔗 添加远程仓库...
git remote add origin %GITLAB_URL%

echo 📤 推送到GitLab...
git branch -M main
git push -u origin main

if %errorlevel% equ 0 (
    echo.
    echo 🎉 成功推送到GitLab仓库！
    echo 📍 仓库地址: %GITLAB_URL%
    echo.
    echo 📋 后续操作建议:
    echo 1. 在GitLab上设置仓库描述和标签
    echo 2. 添加README.md到仓库首页展示
    echo 3. 设置仓库可见性（私有/公开）
    echo 4. 配置CI/CD流水线（可选）
    echo.
) else (
    echo.
    echo ❌ 推送失败，可能的原因:
    echo 1. 仓库URL不正确
    echo 2. 没有推送权限
    echo 3. 网络连接问题
    echo 4. 需要先在GitLab上创建空仓库
    echo.
    echo 💡 解决方案:
    echo 1. 检查GitLab仓库是否已创建
    echo 2. 确认仓库URL正确
    echo 3. 检查Git凭据配置
    echo.
)

pause
