# -*- coding: utf-8 -*-
"""
Instagram私信数据提取和分析主程序
"""

import os
import sys
import argparse
import logging
from datetime import datetime

from instagram_extractor import InstagramExtractor
from data_analyzer import DataAnalyzer
from config import Config

def setup_logging():
    """设置日志配置"""
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    logging.basicConfig(
        level=logging.INFO,
        format=log_format,
        handlers=[
            logging.FileHandler('instagram_dm_tool.log', encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )

def check_environment():
    """检查环境配置"""
    config = Config()
    
    missing_configs = []
    
    if not config.INSTAGRAM_USERNAME:
        missing_configs.append("INSTAGRAM_USERNAME")
    
    if not config.INSTAGRAM_PASSWORD:
        missing_configs.append("INSTAGRAM_PASSWORD")
    
    if not config.TARGET_USERNAME:
        missing_configs.append("TARGET_USERNAME")
    
    if missing_configs:
        print("❌ 缺少必要的配置项:")
        for config_name in missing_configs:
            print(f"   - {config_name}")
        print("\n请在.env文件中设置这些配置项，或者参考.env.example文件")
        return False
    
    print("✅ 环境配置检查通过")
    return True

def extract_data(target_username=None):
    """提取Instagram私信数据"""
    print("🚀 开始提取Instagram私信数据...")
    
    extractor = InstagramExtractor()
    success = extractor.extract_conversation(target_username)
    
    if success:
        print("✅ 数据提取完成!")
        return True
    else:
        print("❌ 数据提取失败!")
        return False

def analyze_data(data_file=None):
    """分析提取的数据"""
    print("📊 开始分析数据...")
    
    if not data_file:
        # 查找最新的数据文件
        output_dir = Config().OUTPUT_DIR
        json_files = [f for f in os.listdir(output_dir) if f.startswith('instagram_dm_') and f.endswith('.json')]
        
        if not json_files:
            print("❌ 未找到数据文件，请先运行数据提取")
            return False
        
        # 选择最新的文件
        json_files.sort(reverse=True)
        data_file = os.path.join(output_dir, json_files[0])
        print(f"📁 使用数据文件: {data_file}")
    
    try:
        analyzer = DataAnalyzer(data_file)
        report_file = analyzer.generate_comprehensive_report()
        print(f"✅ 数据分析完成! 报告保存在: {report_file}")
        return True
    except Exception as e:
        print(f"❌ 数据分析失败: {str(e)}")
        return False

def print_banner():
    """打印程序横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                Instagram私信数据提取和分析工具                ║
║                                                              ║
║  功能:                                                       ║
║  • 自动提取Instagram私信对话数据                              ║
║  • 下载对话中的媒体文件                                       ║
║  • 生成详细的数据分析报告                                     ║
║  • 创建可视化图表和统计信息                                   ║
║                                                              ║
║  注意: 请确保遵守Instagram的使用条款和相关法律法规             ║
╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Instagram私信数据提取和分析工具')
    parser.add_argument('--extract', action='store_true', help='提取Instagram私信数据')
    parser.add_argument('--analyze', action='store_true', help='分析已提取的数据')
    parser.add_argument('--target', type=str, help='目标用户名 (仅用于提取)')
    parser.add_argument('--data-file', type=str, help='指定要分析的数据文件路径')
    parser.add_argument('--all', action='store_true', help='执行完整流程 (提取+分析)')
    
    args = parser.parse_args()
    
    # 设置日志
    setup_logging()
    
    # 打印横幅
    print_banner()
    
    # 检查环境
    if not check_environment():
        sys.exit(1)
    
    # 根据参数执行相应操作
    if args.all:
        print("🔄 执行完整流程...")
        if extract_data(args.target):
            analyze_data(args.data_file)
    elif args.extract:
        extract_data(args.target)
    elif args.analyze:
        analyze_data(args.data_file)
    else:
        # 交互式菜单
        show_interactive_menu()

def show_interactive_menu():
    """显示交互式菜单"""
    while True:
        print("\n" + "="*60)
        print("请选择操作:")
        print("1. 提取Instagram私信数据")
        print("2. 分析已提取的数据")
        print("3. 执行完整流程 (提取+分析)")
        print("4. 查看配置信息")
        print("5. 退出")
        print("="*60)
        
        choice = input("请输入选项 (1-5): ").strip()
        
        if choice == '1':
            target = input("请输入目标用户名 (留空使用配置文件中的设置): ").strip()
            extract_data(target if target else None)
        
        elif choice == '2':
            data_file = input("请输入数据文件路径 (留空自动选择最新文件): ").strip()
            analyze_data(data_file if data_file else None)
        
        elif choice == '3':
            target = input("请输入目标用户名 (留空使用配置文件中的设置): ").strip()
            if extract_data(target if target else None):
                analyze_data()
        
        elif choice == '4':
            show_config_info()
        
        elif choice == '5':
            print("👋 再见!")
            break
        
        else:
            print("❌ 无效选项，请重新选择")

def show_config_info():
    """显示配置信息"""
    config = Config()
    
    print("\n" + "="*60)
    print("当前配置信息:")
    print(f"Instagram用户名: {config.INSTAGRAM_USERNAME}")
    print(f"目标用户名: {config.TARGET_USERNAME}")
    print(f"最大消息数: {config.MAX_MESSAGES}")
    print(f"输出目录: {config.OUTPUT_DIR}")
    print(f"无头模式: {config.HEADLESS_MODE}")
    print(f"情感分析: {config.SENTIMENT_ANALYSIS}")
    print(f"生成词云: {config.GENERATE_WORDCLOUD}")
    print("="*60)

if __name__ == "__main__":
    main()
