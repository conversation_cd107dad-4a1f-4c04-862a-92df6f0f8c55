#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
代码修复脚本
修复Instagram私信提取工具中的常见问题
"""

import os
import sys
import subprocess
import importlib.util

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        print("❌ Python版本过低，需要Python 3.8+")
        print(f"当前版本: {sys.version}")
        return False
    print(f"✅ Python版本检查通过: {sys.version}")
    return True

def check_and_install_packages():
    """检查并安装缺失的包"""
    required_packages = [
        'selenium',
        'webdriver-manager', 
        'beautifulsoup4',
        'pandas',
        'numpy',
        'matplotlib',
        'seaborn',
        'plotly',
        'textblob',
        'wordcloud',
        'requests',
        'python-dotenv',
        'tqdm',
        'pillow'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            # 特殊处理一些包名映射
            import_name = package
            if package == 'beautifulsoup4':
                import_name = 'bs4'
            elif package == 'python-dotenv':
                import_name = 'dotenv'
            elif package == 'pillow':
                import_name = 'PIL'
            
            spec = importlib.util.find_spec(import_name)
            if spec is None:
                missing_packages.append(package)
            else:
                print(f"✅ {package} 已安装")
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ 缺少以下包: {', '.join(missing_packages)}")
        print("正在安装缺失的包...")
        
        for package in missing_packages:
            try:
                subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
                print(f"✅ {package} 安装成功")
            except subprocess.CalledProcessError:
                print(f"❌ {package} 安装失败")
                return False
    
    print("✅ 所有必需包已安装")
    return True

def create_env_file():
    """创建.env配置文件"""
    if os.path.exists('.env'):
        print("✅ .env文件已存在")
        return True
    
    if not os.path.exists('.env.example'):
        # 创建.env.example文件
        env_content = """# Instagram私信数据提取工具配置文件

# Instagram登录信息
INSTAGRAM_USERNAME=你的Instagram用户名
INSTAGRAM_PASSWORD=你的Instagram密码
TARGET_USERNAME=目标用户名

# 提取设置
MAX_MESSAGES=1000
HEADLESS_MODE=False
SCROLL_DELAY=2.0
LOAD_DELAY=3.0

# 分析设置
SENTIMENT_ANALYSIS=True
GENERATE_WORDCLOUD=True
GENERATE_VISUALIZATIONS=True

# 输出设置
OUTPUT_DIR=output
BACKUP_DIR=backup
"""
        with open('.env.example', 'w', encoding='utf-8') as f:
            f.write(env_content)
        print("✅ 创建了.env.example文件")
    
    # 复制.env.example为.env
    try:
        with open('.env.example', 'r', encoding='utf-8') as f:
            content = f.read()
        with open('.env', 'w', encoding='utf-8') as f:
            f.write(content)
        print("✅ 创建了.env文件")
        print("⚠️  请编辑.env文件，填写您的Instagram登录信息")
        return True
    except Exception as e:
        print(f"❌ 创建.env文件失败: {e}")
        return False

def create_directories():
    """创建必要的目录"""
    directories = ['output', 'backup', 'output/media', 'visualizations']
    
    for directory in directories:
        if not os.path.exists(directory):
            try:
                os.makedirs(directory, exist_ok=True)
                print(f"✅ 创建目录: {directory}")
            except Exception as e:
                print(f"❌ 创建目录失败 {directory}: {e}")
                return False
    
    print("✅ 所有必要目录已创建")
    return True

def fix_import_issues():
    """修复导入问题"""
    print("🔧 检查导入问题...")
    
    # 检查主要模块文件是否存在
    required_files = [
        'main.py',
        'instagram_extractor.py', 
        'data_analyzer.py',
        'config.py',
        'utils.py',
        'instagram_selectors.py'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ 缺少文件: {', '.join(missing_files)}")
        return False
    
    print("✅ 所有必要文件都存在")
    return True

def test_basic_functionality():
    """测试基本功能"""
    print("🧪 测试基本功能...")
    
    try:
        # 测试配置加载
        from config import Config
        config = Config()
        print("✅ 配置模块加载成功")
        
        # 测试选择器
        from instagram_selectors import InstagramSelectors
        selectors = InstagramSelectors()
        print("✅ 选择器模块加载成功")
        
        # 测试工具函数
        from utils import random_delay, format_file_size
        print("✅ 工具模块加载成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 基本功能测试失败: {e}")
        return False

def main():
    """主修复流程"""
    print("🔧 Instagram私信提取工具 - 代码修复脚本")
    print("=" * 50)
    
    # 检查Python版本
    if not check_python_version():
        return False
    
    # 检查并安装包
    if not check_and_install_packages():
        return False
    
    # 创建配置文件
    if not create_env_file():
        return False
    
    # 创建目录
    if not create_directories():
        return False
    
    # 修复导入问题
    if not fix_import_issues():
        return False
    
    # 测试基本功能
    if not test_basic_functionality():
        return False
    
    print("\n🎉 代码修复完成！")
    print("\n📋 下一步操作:")
    print("1. 编辑.env文件，填写您的Instagram登录信息")
    print("2. 运行 python main.py 开始使用工具")
    print("3. 或运行 python demo.py 查看演示")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n❌ 修复过程中遇到问题，请检查错误信息")
        sys.exit(1)
    else:
        print("\n✅ 修复成功完成！")
