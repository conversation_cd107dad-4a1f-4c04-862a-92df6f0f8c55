2025-07-31 22:59:57,492 - INFO - NumExpr defaulting to 16 threads.
2025-07-31 23:01:27,028 - INFO - NumExpr defaulting to 16 threads.
2025-07-31 23:01:54,322 - INFO - NumExpr defaulting to 16 threads.
2025-07-31 23:02:15,693 - INFO - 正在设置Chrome浏览器驱动...
2025-07-31 23:02:15,693 - INFO - ====== WebDriver manager ======
2025-07-31 23:02:16,533 - INFO - Get LATEST chromedriver version for google-chrome
2025-07-31 23:02:17,070 - INFO - Get LATEST chromedriver version for google-chrome
2025-07-31 23:02:18,668 - INFO - There is no [win64] chromedriver "138.0.7204.183" for browser google-chrome "138.0.7204" in cache
2025-07-31 23:02:18,669 - INFO - Get LATEST chromedriver version for google-chrome
2025-07-31 23:02:19,880 - INFO - WebDriver version 138.0.7204.183 selected
2025-07-31 23:02:19,885 - INFO - Modern chrome version https://storage.googleapis.com/chrome-for-testing-public/138.0.7204.183/win32/chromedriver-win32.zip
2025-07-31 23:02:19,885 - INFO - About to download new driver from https://storage.googleapis.com/chrome-for-testing-public/138.0.7204.183/win32/chromedriver-win32.zip
2025-07-31 23:02:20,446 - INFO - Driver downloading response is 200
2025-07-31 23:02:21,775 - INFO - Get LATEST chromedriver version for google-chrome
2025-07-31 23:02:23,411 - INFO - Driver has been saved in cache [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\138.0.7204.183]
2025-07-31 23:02:25,633 - INFO - Chrome浏览器驱动设置完成
2025-07-31 23:02:25,633 - INFO - 正在登录Instagram...
2025-07-31 23:02:42,946 - WARNING - 未找到可点击元素: ['button[aria-label="关闭"]', 'button[aria-label="Close"]', 'svg[aria-label="关闭"]', 'svg[aria-label="Close"]']
2025-07-31 23:02:42,959 - WARNING - 函数 find_clickable_element 第 1 次重试，错误: Message: invalid selector: An invalid or illegal selector was specified
  (Session info: chrome=138.0.7204.183); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidselectorexception
Stacktrace:
	GetHandleVerifier [0x0x74ba83+63395]
	GetHandleVerifier [0x0x74bac4+63460]
	(No symbol) [0x0x592113]
	(No symbol) [0x0x5988e9]
	(No symbol) [0x0x59ac7a]
	(No symbol) [0x0x59acf7]
	(No symbol) [0x0x5da0f4]
	(No symbol) [0x0x5dabfb]
	(No symbol) [0x0x622f92]
	(No symbol) [0x0x5ff3f4]
	(No symbol) [0x0x6207ba]
	(No symbol) [0x0x5ff1a6]
	(No symbol) [0x0x5ce7b2]
	(No symbol) [0x0x5cf654]
	GetHandleVerifier [0x0x9c8883+2672035]
	GetHandleVerifier [0x0x9c3cba+2652634]
	GetHandleVerifier [0x0x772bca+223466]
	GetHandleVerifier [0x0x762cb8+158168]
	GetHandleVerifier [0x0x76978d+185517]
	GetHandleVerifier [0x0x753b78+96408]
	GetHandleVerifier [0x0x753d02+96802]
	GetHandleVerifier [0x0x73e90a+9770]
	BaseThreadInitThunk [0x0x76615d49+25]
	RtlInitializeExceptionChain [0x0x772bd1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x772bd131+561]

2025-07-31 23:02:43,966 - WARNING - 函数 find_clickable_element 第 2 次重试，错误: Message: invalid selector: An invalid or illegal selector was specified
  (Session info: chrome=138.0.7204.183); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidselectorexception
Stacktrace:
	GetHandleVerifier [0x0x74ba83+63395]
	GetHandleVerifier [0x0x74bac4+63460]
	(No symbol) [0x0x592113]
	(No symbol) [0x0x5988e9]
	(No symbol) [0x0x59ac7a]
	(No symbol) [0x0x59acf7]
	(No symbol) [0x0x5da0f4]
	(No symbol) [0x0x5dabfb]
	(No symbol) [0x0x622f92]
	(No symbol) [0x0x5ff3f4]
	(No symbol) [0x0x6207ba]
	(No symbol) [0x0x5ff1a6]
	(No symbol) [0x0x5ce7b2]
	(No symbol) [0x0x5cf654]
	GetHandleVerifier [0x0x9c8883+2672035]
	GetHandleVerifier [0x0x9c3cba+2652634]
	GetHandleVerifier [0x0x772bca+223466]
	GetHandleVerifier [0x0x762cb8+158168]
	GetHandleVerifier [0x0x76978d+185517]
	GetHandleVerifier [0x0x753b78+96408]
	GetHandleVerifier [0x0x753d02+96802]
	GetHandleVerifier [0x0x73e90a+9770]
	BaseThreadInitThunk [0x0x76615d49+25]
	RtlInitializeExceptionChain [0x0x772bd1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x772bd131+561]

2025-07-31 23:02:45,974 - ERROR - 函数 find_clickable_element 重试 3 次后仍然失败: Message: invalid selector: An invalid or illegal selector was specified
  (Session info: chrome=138.0.7204.183); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidselectorexception
Stacktrace:
	GetHandleVerifier [0x0x74ba83+63395]
	GetHandleVerifier [0x0x74bac4+63460]
	(No symbol) [0x0x592113]
	(No symbol) [0x0x5988e9]
	(No symbol) [0x0x59ac7a]
	(No symbol) [0x0x59acf7]
	(No symbol) [0x0x5da0f4]
	(No symbol) [0x0x5dabfb]
	(No symbol) [0x0x622f92]
	(No symbol) [0x0x5ff3f4]
	(No symbol) [0x0x6207ba]
	(No symbol) [0x0x5ff1a6]
	(No symbol) [0x0x5ce7b2]
	(No symbol) [0x0x5cf654]
	GetHandleVerifier [0x0x9c8883+2672035]
	GetHandleVerifier [0x0x9c3cba+2652634]
	GetHandleVerifier [0x0x772bca+223466]
	GetHandleVerifier [0x0x762cb8+158168]
	GetHandleVerifier [0x0x76978d+185517]
	GetHandleVerifier [0x0x753b78+96408]
	GetHandleVerifier [0x0x753d02+96802]
	GetHandleVerifier [0x0x73e90a+9770]
	BaseThreadInitThunk [0x0x76615d49+25]
	RtlInitializeExceptionChain [0x0x772bd1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x772bd131+561]

2025-07-31 23:02:45,974 - ERROR - 登录过程中发生错误: Message: invalid selector: An invalid or illegal selector was specified
  (Session info: chrome=138.0.7204.183); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidselectorexception
Stacktrace:
	GetHandleVerifier [0x0x74ba83+63395]
	GetHandleVerifier [0x0x74bac4+63460]
	(No symbol) [0x0x592113]
	(No symbol) [0x0x5988e9]
	(No symbol) [0x0x59ac7a]
	(No symbol) [0x0x59acf7]
	(No symbol) [0x0x5da0f4]
	(No symbol) [0x0x5dabfb]
	(No symbol) [0x0x622f92]
	(No symbol) [0x0x5ff3f4]
	(No symbol) [0x0x6207ba]
	(No symbol) [0x0x5ff1a6]
	(No symbol) [0x0x5ce7b2]
	(No symbol) [0x0x5cf654]
	GetHandleVerifier [0x0x9c8883+2672035]
	GetHandleVerifier [0x0x9c3cba+2652634]
	GetHandleVerifier [0x0x772bca+223466]
	GetHandleVerifier [0x0x762cb8+158168]
	GetHandleVerifier [0x0x76978d+185517]
	GetHandleVerifier [0x0x753b78+96408]
	GetHandleVerifier [0x0x753d02+96802]
	GetHandleVerifier [0x0x73e90a+9770]
	BaseThreadInitThunk [0x0x76615d49+25]
	RtlInitializeExceptionChain [0x0x772bd1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x772bd131+561]

2025-07-31 23:03:02,937 - INFO - 正在设置Chrome浏览器驱动...
2025-07-31 23:03:02,937 - INFO - ====== WebDriver manager ======
2025-07-31 23:03:03,760 - INFO - Get LATEST chromedriver version for google-chrome
2025-07-31 23:03:04,289 - INFO - Get LATEST chromedriver version for google-chrome
2025-07-31 23:03:04,870 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\138.0.7204.183\chromedriver-win32/chromedriver.exe] found in cache
2025-07-31 23:03:05,938 - INFO - Chrome浏览器驱动设置完成
2025-07-31 23:03:05,938 - INFO - 正在登录Instagram...
2025-07-31 23:03:18,619 - INFO - 关闭了弹窗
2025-07-31 23:03:18,634 - WARNING - 函数 find_clickable_element 第 1 次重试，错误: Message: invalid selector: An invalid or illegal selector was specified
  (Session info: chrome=138.0.7204.183); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidselectorexception
Stacktrace:
	GetHandleVerifier [0x0x74ba83+63395]
	GetHandleVerifier [0x0x74bac4+63460]
	(No symbol) [0x0x592113]
	(No symbol) [0x0x5988e9]
	(No symbol) [0x0x59ac7a]
	(No symbol) [0x0x59acf7]
	(No symbol) [0x0x5da0f4]
	(No symbol) [0x0x5dabfb]
	(No symbol) [0x0x622f92]
	(No symbol) [0x0x5ff3f4]
	(No symbol) [0x0x6207ba]
	(No symbol) [0x0x5ff1a6]
	(No symbol) [0x0x5ce7b2]
	(No symbol) [0x0x5cf654]
	GetHandleVerifier [0x0x9c8883+2672035]
	GetHandleVerifier [0x0x9c3cba+2652634]
	GetHandleVerifier [0x0x772bca+223466]
	GetHandleVerifier [0x0x762cb8+158168]
	GetHandleVerifier [0x0x76978d+185517]
	GetHandleVerifier [0x0x753b78+96408]
	GetHandleVerifier [0x0x753d02+96802]
	GetHandleVerifier [0x0x73e90a+9770]
	BaseThreadInitThunk [0x0x76615d49+25]
	RtlInitializeExceptionChain [0x0x772bd1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x772bd131+561]

2025-07-31 23:03:19,643 - WARNING - 函数 find_clickable_element 第 2 次重试，错误: Message: invalid selector: An invalid or illegal selector was specified
  (Session info: chrome=138.0.7204.183); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidselectorexception
Stacktrace:
	GetHandleVerifier [0x0x74ba83+63395]
	GetHandleVerifier [0x0x74bac4+63460]
	(No symbol) [0x0x592113]
	(No symbol) [0x0x5988e9]
	(No symbol) [0x0x59ac7a]
	(No symbol) [0x0x59acf7]
	(No symbol) [0x0x5da0f4]
	(No symbol) [0x0x5dabfb]
	(No symbol) [0x0x622f92]
	(No symbol) [0x0x5ff3f4]
	(No symbol) [0x0x6207ba]
	(No symbol) [0x0x5ff1a6]
	(No symbol) [0x0x5ce7b2]
	(No symbol) [0x0x5cf654]
	GetHandleVerifier [0x0x9c8883+2672035]
	GetHandleVerifier [0x0x9c3cba+2652634]
	GetHandleVerifier [0x0x772bca+223466]
	GetHandleVerifier [0x0x762cb8+158168]
	GetHandleVerifier [0x0x76978d+185517]
	GetHandleVerifier [0x0x753b78+96408]
	GetHandleVerifier [0x0x753d02+96802]
	GetHandleVerifier [0x0x73e90a+9770]
	BaseThreadInitThunk [0x0x76615d49+25]
	RtlInitializeExceptionChain [0x0x772bd1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x772bd131+561]

2025-07-31 23:03:21,649 - ERROR - 函数 find_clickable_element 重试 3 次后仍然失败: Message: invalid selector: An invalid or illegal selector was specified
  (Session info: chrome=138.0.7204.183); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidselectorexception
Stacktrace:
	GetHandleVerifier [0x0x74ba83+63395]
	GetHandleVerifier [0x0x74bac4+63460]
	(No symbol) [0x0x592113]
	(No symbol) [0x0x5988e9]
	(No symbol) [0x0x59ac7a]
	(No symbol) [0x0x59acf7]
	(No symbol) [0x0x5da0f4]
	(No symbol) [0x0x5dabfb]
	(No symbol) [0x0x622f92]
	(No symbol) [0x0x5ff3f4]
	(No symbol) [0x0x6207ba]
	(No symbol) [0x0x5ff1a6]
	(No symbol) [0x0x5ce7b2]
	(No symbol) [0x0x5cf654]
	GetHandleVerifier [0x0x9c8883+2672035]
	GetHandleVerifier [0x0x9c3cba+2652634]
	GetHandleVerifier [0x0x772bca+223466]
	GetHandleVerifier [0x0x762cb8+158168]
	GetHandleVerifier [0x0x76978d+185517]
	GetHandleVerifier [0x0x753b78+96408]
	GetHandleVerifier [0x0x753d02+96802]
	GetHandleVerifier [0x0x73e90a+9770]
	BaseThreadInitThunk [0x0x76615d49+25]
	RtlInitializeExceptionChain [0x0x772bd1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x772bd131+561]

2025-07-31 23:03:21,650 - ERROR - 登录过程中发生错误: Message: invalid selector: An invalid or illegal selector was specified
  (Session info: chrome=138.0.7204.183); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidselectorexception
Stacktrace:
	GetHandleVerifier [0x0x74ba83+63395]
	GetHandleVerifier [0x0x74bac4+63460]
	(No symbol) [0x0x592113]
	(No symbol) [0x0x5988e9]
	(No symbol) [0x0x59ac7a]
	(No symbol) [0x0x59acf7]
	(No symbol) [0x0x5da0f4]
	(No symbol) [0x0x5dabfb]
	(No symbol) [0x0x622f92]
	(No symbol) [0x0x5ff3f4]
	(No symbol) [0x0x6207ba]
	(No symbol) [0x0x5ff1a6]
	(No symbol) [0x0x5ce7b2]
	(No symbol) [0x0x5cf654]
	GetHandleVerifier [0x0x9c8883+2672035]
	GetHandleVerifier [0x0x9c3cba+2652634]
	GetHandleVerifier [0x0x772bca+223466]
	GetHandleVerifier [0x0x762cb8+158168]
	GetHandleVerifier [0x0x76978d+185517]
	GetHandleVerifier [0x0x753b78+96408]
	GetHandleVerifier [0x0x753d02+96802]
	GetHandleVerifier [0x0x73e90a+9770]
	BaseThreadInitThunk [0x0x76615d49+25]
	RtlInitializeExceptionChain [0x0x772bd1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x772bd131+561]

2025-07-31 23:05:29,644 - INFO - NumExpr defaulting to 16 threads.
2025-07-31 23:05:34,752 - INFO - 正在设置Chrome浏览器驱动...
2025-07-31 23:05:34,752 - INFO - ====== WebDriver manager ======
2025-07-31 23:05:35,554 - INFO - Get LATEST chromedriver version for google-chrome
2025-07-31 23:05:36,377 - INFO - Get LATEST chromedriver version for google-chrome
2025-07-31 23:05:37,235 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\138.0.7204.183\chromedriver-win32/chromedriver.exe] found in cache
2025-07-31 23:05:38,294 - INFO - Chrome浏览器驱动设置完成
2025-07-31 23:05:38,294 - INFO - 正在登录Instagram...
2025-07-31 23:05:55,118 - INFO - 关闭了弹窗
2025-07-31 23:05:55,134 - WARNING - 函数 find_clickable_element 第 1 次重试，错误: Message: invalid selector: An invalid or illegal selector was specified
  (Session info: chrome=138.0.7204.183); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidselectorexception
Stacktrace:
	GetHandleVerifier [0x0x74ba83+63395]
	GetHandleVerifier [0x0x74bac4+63460]
	(No symbol) [0x0x592113]
	(No symbol) [0x0x5988e9]
	(No symbol) [0x0x59ac7a]
	(No symbol) [0x0x59acf7]
	(No symbol) [0x0x5da0f4]
	(No symbol) [0x0x5dabfb]
	(No symbol) [0x0x622f92]
	(No symbol) [0x0x5ff3f4]
	(No symbol) [0x0x6207ba]
	(No symbol) [0x0x5ff1a6]
	(No symbol) [0x0x5ce7b2]
	(No symbol) [0x0x5cf654]
	GetHandleVerifier [0x0x9c8883+2672035]
	GetHandleVerifier [0x0x9c3cba+2652634]
	GetHandleVerifier [0x0x772bca+223466]
	GetHandleVerifier [0x0x762cb8+158168]
	GetHandleVerifier [0x0x76978d+185517]
	GetHandleVerifier [0x0x753b78+96408]
	GetHandleVerifier [0x0x753d02+96802]
	GetHandleVerifier [0x0x73e90a+9770]
	BaseThreadInitThunk [0x0x76615d49+25]
	RtlInitializeExceptionChain [0x0x772bd1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x772bd131+561]

2025-07-31 23:05:57,658 - WARNING - 函数 find_clickable_element 第 2 次重试，错误: Message: invalid selector: An invalid or illegal selector was specified
  (Session info: chrome=138.0.7204.183); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidselectorexception
Stacktrace:
	GetHandleVerifier [0x0x74ba83+63395]
	GetHandleVerifier [0x0x74bac4+63460]
	(No symbol) [0x0x592113]
	(No symbol) [0x0x5988e9]
	(No symbol) [0x0x59ac7a]
	(No symbol) [0x0x59acf7]
	(No symbol) [0x0x5da0f4]
	(No symbol) [0x0x5dabfb]
	(No symbol) [0x0x622f92]
	(No symbol) [0x0x5ff3f4]
	(No symbol) [0x0x6207ba]
	(No symbol) [0x0x5ff1a6]
	(No symbol) [0x0x5ce7b2]
	(No symbol) [0x0x5cf654]
	GetHandleVerifier [0x0x9c8883+2672035]
	GetHandleVerifier [0x0x9c3cba+2652634]
	GetHandleVerifier [0x0x772bca+223466]
	GetHandleVerifier [0x0x762cb8+158168]
	GetHandleVerifier [0x0x76978d+185517]
	GetHandleVerifier [0x0x753b78+96408]
	GetHandleVerifier [0x0x753d02+96802]
	GetHandleVerifier [0x0x73e90a+9770]
	BaseThreadInitThunk [0x0x76615d49+25]
	RtlInitializeExceptionChain [0x0x772bd1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x772bd131+561]

2025-07-31 23:05:59,669 - ERROR - 函数 find_clickable_element 重试 3 次后仍然失败: Message: invalid selector: An invalid or illegal selector was specified
  (Session info: chrome=138.0.7204.183); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidselectorexception
Stacktrace:
	GetHandleVerifier [0x0x74ba83+63395]
	GetHandleVerifier [0x0x74bac4+63460]
	(No symbol) [0x0x592113]
	(No symbol) [0x0x5988e9]
	(No symbol) [0x0x59ac7a]
	(No symbol) [0x0x59acf7]
	(No symbol) [0x0x5da0f4]
	(No symbol) [0x0x5dabfb]
	(No symbol) [0x0x622f92]
	(No symbol) [0x0x5ff3f4]
	(No symbol) [0x0x6207ba]
	(No symbol) [0x0x5ff1a6]
	(No symbol) [0x0x5ce7b2]
	(No symbol) [0x0x5cf654]
	GetHandleVerifier [0x0x9c8883+2672035]
	GetHandleVerifier [0x0x9c3cba+2652634]
	GetHandleVerifier [0x0x772bca+223466]
	GetHandleVerifier [0x0x762cb8+158168]
	GetHandleVerifier [0x0x76978d+185517]
	GetHandleVerifier [0x0x753b78+96408]
	GetHandleVerifier [0x0x753d02+96802]
	GetHandleVerifier [0x0x73e90a+9770]
	BaseThreadInitThunk [0x0x76615d49+25]
	RtlInitializeExceptionChain [0x0x772bd1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x772bd131+561]

2025-07-31 23:05:59,669 - ERROR - 登录过程中发生错误: Message: invalid selector: An invalid or illegal selector was specified
  (Session info: chrome=138.0.7204.183); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidselectorexception
Stacktrace:
	GetHandleVerifier [0x0x74ba83+63395]
	GetHandleVerifier [0x0x74bac4+63460]
	(No symbol) [0x0x592113]
	(No symbol) [0x0x5988e9]
	(No symbol) [0x0x59ac7a]
	(No symbol) [0x0x59acf7]
	(No symbol) [0x0x5da0f4]
	(No symbol) [0x0x5dabfb]
	(No symbol) [0x0x622f92]
	(No symbol) [0x0x5ff3f4]
	(No symbol) [0x0x6207ba]
	(No symbol) [0x0x5ff1a6]
	(No symbol) [0x0x5ce7b2]
	(No symbol) [0x0x5cf654]
	GetHandleVerifier [0x0x9c8883+2672035]
	GetHandleVerifier [0x0x9c3cba+2652634]
	GetHandleVerifier [0x0x772bca+223466]
	GetHandleVerifier [0x0x762cb8+158168]
	GetHandleVerifier [0x0x76978d+185517]
	GetHandleVerifier [0x0x753b78+96408]
	GetHandleVerifier [0x0x753d02+96802]
	GetHandleVerifier [0x0x73e90a+9770]
	BaseThreadInitThunk [0x0x76615d49+25]
	RtlInitializeExceptionChain [0x0x772bd1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x772bd131+561]

2025-07-31 23:06:06,083 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001F3F8D211D0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')': /session/73197dd4d83388281fbac1a5eeaa188b
2025-07-31 23:06:10,183 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001F3F8D21A90>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')': /session/73197dd4d83388281fbac1a5eeaa188b
