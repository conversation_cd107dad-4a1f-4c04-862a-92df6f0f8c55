2025-07-31 22:59:57,492 - INFO - NumExpr defaulting to 16 threads.
2025-07-31 23:01:27,028 - INFO - NumExpr defaulting to 16 threads.
2025-07-31 23:01:54,322 - INFO - NumExpr defaulting to 16 threads.
2025-07-31 23:02:15,693 - INFO - 正在设置Chrome浏览器驱动...
2025-07-31 23:02:15,693 - INFO - ====== WebDriver manager ======
2025-07-31 23:02:16,533 - INFO - Get LATEST chromedriver version for google-chrome
2025-07-31 23:02:17,070 - INFO - Get LATEST chromedriver version for google-chrome
2025-07-31 23:02:18,668 - INFO - There is no [win64] chromedriver "138.0.7204.183" for browser google-chrome "138.0.7204" in cache
2025-07-31 23:02:18,669 - INFO - Get LATEST chromedriver version for google-chrome
2025-07-31 23:02:19,880 - INFO - WebDriver version 138.0.7204.183 selected
2025-07-31 23:02:19,885 - INFO - Modern chrome version https://storage.googleapis.com/chrome-for-testing-public/138.0.7204.183/win32/chromedriver-win32.zip
2025-07-31 23:02:19,885 - INFO - About to download new driver from https://storage.googleapis.com/chrome-for-testing-public/138.0.7204.183/win32/chromedriver-win32.zip
2025-07-31 23:02:20,446 - INFO - Driver downloading response is 200
2025-07-31 23:02:21,775 - INFO - Get LATEST chromedriver version for google-chrome
2025-07-31 23:02:23,411 - INFO - Driver has been saved in cache [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\138.0.7204.183]
2025-07-31 23:02:25,633 - INFO - Chrome浏览器驱动设置完成
2025-07-31 23:02:25,633 - INFO - 正在登录Instagram...
2025-07-31 23:02:42,946 - WARNING - 未找到可点击元素: ['button[aria-label="关闭"]', 'button[aria-label="Close"]', 'svg[aria-label="关闭"]', 'svg[aria-label="Close"]']
2025-07-31 23:02:42,959 - WARNING - 函数 find_clickable_element 第 1 次重试，错误: Message: invalid selector: An invalid or illegal selector was specified
  (Session info: chrome=138.0.7204.183); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidselectorexception
Stacktrace:
	GetHandleVerifier [0x0x74ba83+63395]
	GetHandleVerifier [0x0x74bac4+63460]
	(No symbol) [0x0x592113]
	(No symbol) [0x0x5988e9]
	(No symbol) [0x0x59ac7a]
	(No symbol) [0x0x59acf7]
	(No symbol) [0x0x5da0f4]
	(No symbol) [0x0x5dabfb]
	(No symbol) [0x0x622f92]
	(No symbol) [0x0x5ff3f4]
	(No symbol) [0x0x6207ba]
	(No symbol) [0x0x5ff1a6]
	(No symbol) [0x0x5ce7b2]
	(No symbol) [0x0x5cf654]
	GetHandleVerifier [0x0x9c8883+2672035]
	GetHandleVerifier [0x0x9c3cba+2652634]
	GetHandleVerifier [0x0x772bca+223466]
	GetHandleVerifier [0x0x762cb8+158168]
	GetHandleVerifier [0x0x76978d+185517]
	GetHandleVerifier [0x0x753b78+96408]
	GetHandleVerifier [0x0x753d02+96802]
	GetHandleVerifier [0x0x73e90a+9770]
	BaseThreadInitThunk [0x0x76615d49+25]
	RtlInitializeExceptionChain [0x0x772bd1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x772bd131+561]

2025-07-31 23:02:43,966 - WARNING - 函数 find_clickable_element 第 2 次重试，错误: Message: invalid selector: An invalid or illegal selector was specified
  (Session info: chrome=138.0.7204.183); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidselectorexception
Stacktrace:
	GetHandleVerifier [0x0x74ba83+63395]
	GetHandleVerifier [0x0x74bac4+63460]
	(No symbol) [0x0x592113]
	(No symbol) [0x0x5988e9]
	(No symbol) [0x0x59ac7a]
	(No symbol) [0x0x59acf7]
	(No symbol) [0x0x5da0f4]
	(No symbol) [0x0x5dabfb]
	(No symbol) [0x0x622f92]
	(No symbol) [0x0x5ff3f4]
	(No symbol) [0x0x6207ba]
	(No symbol) [0x0x5ff1a6]
	(No symbol) [0x0x5ce7b2]
	(No symbol) [0x0x5cf654]
	GetHandleVerifier [0x0x9c8883+2672035]
	GetHandleVerifier [0x0x9c3cba+2652634]
	GetHandleVerifier [0x0x772bca+223466]
	GetHandleVerifier [0x0x762cb8+158168]
	GetHandleVerifier [0x0x76978d+185517]
	GetHandleVerifier [0x0x753b78+96408]
	GetHandleVerifier [0x0x753d02+96802]
	GetHandleVerifier [0x0x73e90a+9770]
	BaseThreadInitThunk [0x0x76615d49+25]
	RtlInitializeExceptionChain [0x0x772bd1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x772bd131+561]

2025-07-31 23:02:45,974 - ERROR - 函数 find_clickable_element 重试 3 次后仍然失败: Message: invalid selector: An invalid or illegal selector was specified
  (Session info: chrome=138.0.7204.183); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidselectorexception
Stacktrace:
	GetHandleVerifier [0x0x74ba83+63395]
	GetHandleVerifier [0x0x74bac4+63460]
	(No symbol) [0x0x592113]
	(No symbol) [0x0x5988e9]
	(No symbol) [0x0x59ac7a]
	(No symbol) [0x0x59acf7]
	(No symbol) [0x0x5da0f4]
	(No symbol) [0x0x5dabfb]
	(No symbol) [0x0x622f92]
	(No symbol) [0x0x5ff3f4]
	(No symbol) [0x0x6207ba]
	(No symbol) [0x0x5ff1a6]
	(No symbol) [0x0x5ce7b2]
	(No symbol) [0x0x5cf654]
	GetHandleVerifier [0x0x9c8883+2672035]
	GetHandleVerifier [0x0x9c3cba+2652634]
	GetHandleVerifier [0x0x772bca+223466]
	GetHandleVerifier [0x0x762cb8+158168]
	GetHandleVerifier [0x0x76978d+185517]
	GetHandleVerifier [0x0x753b78+96408]
	GetHandleVerifier [0x0x753d02+96802]
	GetHandleVerifier [0x0x73e90a+9770]
	BaseThreadInitThunk [0x0x76615d49+25]
	RtlInitializeExceptionChain [0x0x772bd1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x772bd131+561]

2025-07-31 23:02:45,974 - ERROR - 登录过程中发生错误: Message: invalid selector: An invalid or illegal selector was specified
  (Session info: chrome=138.0.7204.183); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidselectorexception
Stacktrace:
	GetHandleVerifier [0x0x74ba83+63395]
	GetHandleVerifier [0x0x74bac4+63460]
	(No symbol) [0x0x592113]
	(No symbol) [0x0x5988e9]
	(No symbol) [0x0x59ac7a]
	(No symbol) [0x0x59acf7]
	(No symbol) [0x0x5da0f4]
	(No symbol) [0x0x5dabfb]
	(No symbol) [0x0x622f92]
	(No symbol) [0x0x5ff3f4]
	(No symbol) [0x0x6207ba]
	(No symbol) [0x0x5ff1a6]
	(No symbol) [0x0x5ce7b2]
	(No symbol) [0x0x5cf654]
	GetHandleVerifier [0x0x9c8883+2672035]
	GetHandleVerifier [0x0x9c3cba+2652634]
	GetHandleVerifier [0x0x772bca+223466]
	GetHandleVerifier [0x0x762cb8+158168]
	GetHandleVerifier [0x0x76978d+185517]
	GetHandleVerifier [0x0x753b78+96408]
	GetHandleVerifier [0x0x753d02+96802]
	GetHandleVerifier [0x0x73e90a+9770]
	BaseThreadInitThunk [0x0x76615d49+25]
	RtlInitializeExceptionChain [0x0x772bd1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x772bd131+561]

2025-07-31 23:03:02,937 - INFO - 正在设置Chrome浏览器驱动...
2025-07-31 23:03:02,937 - INFO - ====== WebDriver manager ======
2025-07-31 23:03:03,760 - INFO - Get LATEST chromedriver version for google-chrome
2025-07-31 23:03:04,289 - INFO - Get LATEST chromedriver version for google-chrome
2025-07-31 23:03:04,870 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\138.0.7204.183\chromedriver-win32/chromedriver.exe] found in cache
2025-07-31 23:03:05,938 - INFO - Chrome浏览器驱动设置完成
2025-07-31 23:03:05,938 - INFO - 正在登录Instagram...
2025-07-31 23:03:18,619 - INFO - 关闭了弹窗
2025-07-31 23:03:18,634 - WARNING - 函数 find_clickable_element 第 1 次重试，错误: Message: invalid selector: An invalid or illegal selector was specified
  (Session info: chrome=138.0.7204.183); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidselectorexception
Stacktrace:
	GetHandleVerifier [0x0x74ba83+63395]
	GetHandleVerifier [0x0x74bac4+63460]
	(No symbol) [0x0x592113]
	(No symbol) [0x0x5988e9]
	(No symbol) [0x0x59ac7a]
	(No symbol) [0x0x59acf7]
	(No symbol) [0x0x5da0f4]
	(No symbol) [0x0x5dabfb]
	(No symbol) [0x0x622f92]
	(No symbol) [0x0x5ff3f4]
	(No symbol) [0x0x6207ba]
	(No symbol) [0x0x5ff1a6]
	(No symbol) [0x0x5ce7b2]
	(No symbol) [0x0x5cf654]
	GetHandleVerifier [0x0x9c8883+2672035]
	GetHandleVerifier [0x0x9c3cba+2652634]
	GetHandleVerifier [0x0x772bca+223466]
	GetHandleVerifier [0x0x762cb8+158168]
	GetHandleVerifier [0x0x76978d+185517]
	GetHandleVerifier [0x0x753b78+96408]
	GetHandleVerifier [0x0x753d02+96802]
	GetHandleVerifier [0x0x73e90a+9770]
	BaseThreadInitThunk [0x0x76615d49+25]
	RtlInitializeExceptionChain [0x0x772bd1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x772bd131+561]

2025-07-31 23:03:19,643 - WARNING - 函数 find_clickable_element 第 2 次重试，错误: Message: invalid selector: An invalid or illegal selector was specified
  (Session info: chrome=138.0.7204.183); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidselectorexception
Stacktrace:
	GetHandleVerifier [0x0x74ba83+63395]
	GetHandleVerifier [0x0x74bac4+63460]
	(No symbol) [0x0x592113]
	(No symbol) [0x0x5988e9]
	(No symbol) [0x0x59ac7a]
	(No symbol) [0x0x59acf7]
	(No symbol) [0x0x5da0f4]
	(No symbol) [0x0x5dabfb]
	(No symbol) [0x0x622f92]
	(No symbol) [0x0x5ff3f4]
	(No symbol) [0x0x6207ba]
	(No symbol) [0x0x5ff1a6]
	(No symbol) [0x0x5ce7b2]
	(No symbol) [0x0x5cf654]
	GetHandleVerifier [0x0x9c8883+2672035]
	GetHandleVerifier [0x0x9c3cba+2652634]
	GetHandleVerifier [0x0x772bca+223466]
	GetHandleVerifier [0x0x762cb8+158168]
	GetHandleVerifier [0x0x76978d+185517]
	GetHandleVerifier [0x0x753b78+96408]
	GetHandleVerifier [0x0x753d02+96802]
	GetHandleVerifier [0x0x73e90a+9770]
	BaseThreadInitThunk [0x0x76615d49+25]
	RtlInitializeExceptionChain [0x0x772bd1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x772bd131+561]

2025-07-31 23:03:21,649 - ERROR - 函数 find_clickable_element 重试 3 次后仍然失败: Message: invalid selector: An invalid or illegal selector was specified
  (Session info: chrome=138.0.7204.183); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidselectorexception
Stacktrace:
	GetHandleVerifier [0x0x74ba83+63395]
	GetHandleVerifier [0x0x74bac4+63460]
	(No symbol) [0x0x592113]
	(No symbol) [0x0x5988e9]
	(No symbol) [0x0x59ac7a]
	(No symbol) [0x0x59acf7]
	(No symbol) [0x0x5da0f4]
	(No symbol) [0x0x5dabfb]
	(No symbol) [0x0x622f92]
	(No symbol) [0x0x5ff3f4]
	(No symbol) [0x0x6207ba]
	(No symbol) [0x0x5ff1a6]
	(No symbol) [0x0x5ce7b2]
	(No symbol) [0x0x5cf654]
	GetHandleVerifier [0x0x9c8883+2672035]
	GetHandleVerifier [0x0x9c3cba+2652634]
	GetHandleVerifier [0x0x772bca+223466]
	GetHandleVerifier [0x0x762cb8+158168]
	GetHandleVerifier [0x0x76978d+185517]
	GetHandleVerifier [0x0x753b78+96408]
	GetHandleVerifier [0x0x753d02+96802]
	GetHandleVerifier [0x0x73e90a+9770]
	BaseThreadInitThunk [0x0x76615d49+25]
	RtlInitializeExceptionChain [0x0x772bd1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x772bd131+561]

2025-07-31 23:03:21,650 - ERROR - 登录过程中发生错误: Message: invalid selector: An invalid or illegal selector was specified
  (Session info: chrome=138.0.7204.183); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidselectorexception
Stacktrace:
	GetHandleVerifier [0x0x74ba83+63395]
	GetHandleVerifier [0x0x74bac4+63460]
	(No symbol) [0x0x592113]
	(No symbol) [0x0x5988e9]
	(No symbol) [0x0x59ac7a]
	(No symbol) [0x0x59acf7]
	(No symbol) [0x0x5da0f4]
	(No symbol) [0x0x5dabfb]
	(No symbol) [0x0x622f92]
	(No symbol) [0x0x5ff3f4]
	(No symbol) [0x0x6207ba]
	(No symbol) [0x0x5ff1a6]
	(No symbol) [0x0x5ce7b2]
	(No symbol) [0x0x5cf654]
	GetHandleVerifier [0x0x9c8883+2672035]
	GetHandleVerifier [0x0x9c3cba+2652634]
	GetHandleVerifier [0x0x772bca+223466]
	GetHandleVerifier [0x0x762cb8+158168]
	GetHandleVerifier [0x0x76978d+185517]
	GetHandleVerifier [0x0x753b78+96408]
	GetHandleVerifier [0x0x753d02+96802]
	GetHandleVerifier [0x0x73e90a+9770]
	BaseThreadInitThunk [0x0x76615d49+25]
	RtlInitializeExceptionChain [0x0x772bd1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x772bd131+561]

2025-07-31 23:05:29,644 - INFO - NumExpr defaulting to 16 threads.
2025-07-31 23:05:34,752 - INFO - 正在设置Chrome浏览器驱动...
2025-07-31 23:05:34,752 - INFO - ====== WebDriver manager ======
2025-07-31 23:05:35,554 - INFO - Get LATEST chromedriver version for google-chrome
2025-07-31 23:05:36,377 - INFO - Get LATEST chromedriver version for google-chrome
2025-07-31 23:05:37,235 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\138.0.7204.183\chromedriver-win32/chromedriver.exe] found in cache
2025-07-31 23:05:38,294 - INFO - Chrome浏览器驱动设置完成
2025-07-31 23:05:38,294 - INFO - 正在登录Instagram...
2025-07-31 23:05:55,118 - INFO - 关闭了弹窗
2025-07-31 23:05:55,134 - WARNING - 函数 find_clickable_element 第 1 次重试，错误: Message: invalid selector: An invalid or illegal selector was specified
  (Session info: chrome=138.0.7204.183); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidselectorexception
Stacktrace:
	GetHandleVerifier [0x0x74ba83+63395]
	GetHandleVerifier [0x0x74bac4+63460]
	(No symbol) [0x0x592113]
	(No symbol) [0x0x5988e9]
	(No symbol) [0x0x59ac7a]
	(No symbol) [0x0x59acf7]
	(No symbol) [0x0x5da0f4]
	(No symbol) [0x0x5dabfb]
	(No symbol) [0x0x622f92]
	(No symbol) [0x0x5ff3f4]
	(No symbol) [0x0x6207ba]
	(No symbol) [0x0x5ff1a6]
	(No symbol) [0x0x5ce7b2]
	(No symbol) [0x0x5cf654]
	GetHandleVerifier [0x0x9c8883+2672035]
	GetHandleVerifier [0x0x9c3cba+2652634]
	GetHandleVerifier [0x0x772bca+223466]
	GetHandleVerifier [0x0x762cb8+158168]
	GetHandleVerifier [0x0x76978d+185517]
	GetHandleVerifier [0x0x753b78+96408]
	GetHandleVerifier [0x0x753d02+96802]
	GetHandleVerifier [0x0x73e90a+9770]
	BaseThreadInitThunk [0x0x76615d49+25]
	RtlInitializeExceptionChain [0x0x772bd1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x772bd131+561]

2025-07-31 23:05:57,658 - WARNING - 函数 find_clickable_element 第 2 次重试，错误: Message: invalid selector: An invalid or illegal selector was specified
  (Session info: chrome=138.0.7204.183); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidselectorexception
Stacktrace:
	GetHandleVerifier [0x0x74ba83+63395]
	GetHandleVerifier [0x0x74bac4+63460]
	(No symbol) [0x0x592113]
	(No symbol) [0x0x5988e9]
	(No symbol) [0x0x59ac7a]
	(No symbol) [0x0x59acf7]
	(No symbol) [0x0x5da0f4]
	(No symbol) [0x0x5dabfb]
	(No symbol) [0x0x622f92]
	(No symbol) [0x0x5ff3f4]
	(No symbol) [0x0x6207ba]
	(No symbol) [0x0x5ff1a6]
	(No symbol) [0x0x5ce7b2]
	(No symbol) [0x0x5cf654]
	GetHandleVerifier [0x0x9c8883+2672035]
	GetHandleVerifier [0x0x9c3cba+2652634]
	GetHandleVerifier [0x0x772bca+223466]
	GetHandleVerifier [0x0x762cb8+158168]
	GetHandleVerifier [0x0x76978d+185517]
	GetHandleVerifier [0x0x753b78+96408]
	GetHandleVerifier [0x0x753d02+96802]
	GetHandleVerifier [0x0x73e90a+9770]
	BaseThreadInitThunk [0x0x76615d49+25]
	RtlInitializeExceptionChain [0x0x772bd1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x772bd131+561]

2025-07-31 23:05:59,669 - ERROR - 函数 find_clickable_element 重试 3 次后仍然失败: Message: invalid selector: An invalid or illegal selector was specified
  (Session info: chrome=138.0.7204.183); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidselectorexception
Stacktrace:
	GetHandleVerifier [0x0x74ba83+63395]
	GetHandleVerifier [0x0x74bac4+63460]
	(No symbol) [0x0x592113]
	(No symbol) [0x0x5988e9]
	(No symbol) [0x0x59ac7a]
	(No symbol) [0x0x59acf7]
	(No symbol) [0x0x5da0f4]
	(No symbol) [0x0x5dabfb]
	(No symbol) [0x0x622f92]
	(No symbol) [0x0x5ff3f4]
	(No symbol) [0x0x6207ba]
	(No symbol) [0x0x5ff1a6]
	(No symbol) [0x0x5ce7b2]
	(No symbol) [0x0x5cf654]
	GetHandleVerifier [0x0x9c8883+2672035]
	GetHandleVerifier [0x0x9c3cba+2652634]
	GetHandleVerifier [0x0x772bca+223466]
	GetHandleVerifier [0x0x762cb8+158168]
	GetHandleVerifier [0x0x76978d+185517]
	GetHandleVerifier [0x0x753b78+96408]
	GetHandleVerifier [0x0x753d02+96802]
	GetHandleVerifier [0x0x73e90a+9770]
	BaseThreadInitThunk [0x0x76615d49+25]
	RtlInitializeExceptionChain [0x0x772bd1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x772bd131+561]

2025-07-31 23:05:59,669 - ERROR - 登录过程中发生错误: Message: invalid selector: An invalid or illegal selector was specified
  (Session info: chrome=138.0.7204.183); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidselectorexception
Stacktrace:
	GetHandleVerifier [0x0x74ba83+63395]
	GetHandleVerifier [0x0x74bac4+63460]
	(No symbol) [0x0x592113]
	(No symbol) [0x0x5988e9]
	(No symbol) [0x0x59ac7a]
	(No symbol) [0x0x59acf7]
	(No symbol) [0x0x5da0f4]
	(No symbol) [0x0x5dabfb]
	(No symbol) [0x0x622f92]
	(No symbol) [0x0x5ff3f4]
	(No symbol) [0x0x6207ba]
	(No symbol) [0x0x5ff1a6]
	(No symbol) [0x0x5ce7b2]
	(No symbol) [0x0x5cf654]
	GetHandleVerifier [0x0x9c8883+2672035]
	GetHandleVerifier [0x0x9c3cba+2652634]
	GetHandleVerifier [0x0x772bca+223466]
	GetHandleVerifier [0x0x762cb8+158168]
	GetHandleVerifier [0x0x76978d+185517]
	GetHandleVerifier [0x0x753b78+96408]
	GetHandleVerifier [0x0x753d02+96802]
	GetHandleVerifier [0x0x73e90a+9770]
	BaseThreadInitThunk [0x0x76615d49+25]
	RtlInitializeExceptionChain [0x0x772bd1ab+107]
	RtlGetAppContainerNamedObjectPath [0x0x772bd131+561]

2025-07-31 23:06:06,083 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001F3F8D211D0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')': /session/73197dd4d83388281fbac1a5eeaa188b
2025-07-31 23:06:10,183 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001F3F8D21A90>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')': /session/73197dd4d83388281fbac1a5eeaa188b
2025-07-31 23:09:50,523 - INFO - NumExpr defaulting to 16 threads.
2025-07-31 23:09:52,211 - INFO - 正在设置Chrome浏览器驱动...
2025-07-31 23:09:52,212 - INFO - ====== WebDriver manager ======
2025-07-31 23:09:53,071 - INFO - Get LATEST chromedriver version for google-chrome
2025-07-31 23:09:54,036 - INFO - Get LATEST chromedriver version for google-chrome
2025-07-31 23:09:54,865 - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\138.0.7204.183\chromedriver-win32/chromedriver.exe] found in cache
2025-07-31 23:09:55,914 - INFO - Chrome浏览器驱动设置完成
2025-07-31 23:09:55,914 - INFO - 正在登录Instagram...
2025-07-31 23:09:55,915 - INFO - 正在打开Instagram登录页面，请手动完成登录...
2025-07-31 23:10:50,786 - INFO - ✅ 登录验证成功
2025-07-31 23:10:50,786 - INFO - 正在导航到与 _wan.o7 的私信对话...
2025-07-31 23:11:07,298 - INFO - 成功导航到与 _wan.o7 的对话
2025-07-31 23:11:07,298 - INFO - 正在滚动加载历史消息...
2025-07-31 23:11:09,334 - INFO - 滚动完成，共尝试 0 次
2025-07-31 23:11:09,335 - INFO - 正在提取消息数据...
2025-07-31 23:11:09,498 - INFO - 成功提取 118 条消息
2025-07-31 23:11:09,499 - INFO - 正在下载媒体文件...
2025-07-31 23:11:10,078 - INFO - 成功下载媒体文件: _wan.o7_0000_2025-07-31.jpg
2025-07-31 23:11:11,769 - INFO - 成功下载媒体文件: _wan.o7_0001_2025-07-31.jpg
2025-07-31 23:11:12,338 - INFO - 成功下载媒体文件: _wan.o7_0002_2025-07-31.jpg
2025-07-31 23:11:12,934 - INFO - 成功下载媒体文件: _wan.o7_0003_2025-07-31.jpg
2025-07-31 23:11:13,540 - INFO - 成功下载媒体文件: _wan.o7_0004_2025-07-31.jpg
2025-07-31 23:11:14,131 - INFO - 成功下载媒体文件: _wan.o7_0005_2025-07-31.jpg
2025-07-31 23:11:14,640 - INFO - 成功下载媒体文件: _wan.o7_0006_2025-07-31.jpg
2025-07-31 23:11:15,185 - INFO - 成功下载媒体文件: _wan.o7_0007_2025-07-31.jpg
2025-07-31 23:11:15,731 - INFO - 成功下载媒体文件: _wan.o7_0008_2025-07-31.jpg
2025-07-31 23:11:16,255 - INFO - 成功下载媒体文件: _wan.o7_0009_2025-07-31.jpg
2025-07-31 23:11:16,760 - INFO - 成功下载媒体文件: _wan.o7_0010_2025-07-31.jpg
2025-07-31 23:11:17,280 - INFO - 成功下载媒体文件: _wan.o7_0011_2025-07-31.jpg
2025-07-31 23:11:18,816 - INFO - 成功下载媒体文件: _wan.o7_0012_2025-07-31.jpg
2025-07-31 23:11:19,329 - INFO - 成功下载媒体文件: _wan.o7_0013_2025-07-31.jpg
2025-07-31 23:11:19,983 - INFO - 成功下载媒体文件: _wan.o7_0014_2025-07-31.jpg
2025-07-31 23:11:20,496 - INFO - 成功下载媒体文件: _wan.o7_0015_2025-07-31.jpg
2025-07-31 23:11:21,003 - INFO - 成功下载媒体文件: _wan.o7_0016_2025-07-31.jpg
2025-07-31 23:11:21,538 - INFO - 成功下载媒体文件: _wan.o7_0017_2025-07-31.jpg
2025-07-31 23:11:22,948 - INFO - 成功下载媒体文件: _wan.o7_0018_2025-07-31.jpg
2025-07-31 23:11:23,524 - INFO - 成功下载媒体文件: _wan.o7_0019_2025-07-31.jpg
2025-07-31 23:11:24,072 - INFO - 成功下载媒体文件: _wan.o7_0020_2025-07-31.jpg
2025-07-31 23:11:25,565 - INFO - 成功下载媒体文件: _wan.o7_0021_2025-07-31.jpg
2025-07-31 23:11:26,594 - INFO - 成功下载媒体文件: _wan.o7_0022_2025-07-31.jpg
2025-07-31 23:11:27,399 - INFO - 成功下载媒体文件: _wan.o7_0023_2025-07-31.jpg
2025-07-31 23:11:29,119 - INFO - 成功下载媒体文件: _wan.o7_0024_2025-07-31.jpg
2025-07-31 23:11:29,964 - INFO - 成功下载媒体文件: _wan.o7_0025_2025-07-31.jpg
2025-07-31 23:11:31,735 - INFO - 成功下载媒体文件: _wan.o7_0026_2025-07-31.jpg
2025-07-31 23:11:32,563 - INFO - 成功下载媒体文件: _wan.o7_0027_2025-07-31.jpg
2025-07-31 23:11:33,383 - INFO - 成功下载媒体文件: _wan.o7_0028_2025-07-31.jpg
2025-07-31 23:11:35,162 - INFO - 成功下载媒体文件: _wan.o7_0029_2025-07-31.jpg
2025-07-31 23:11:36,004 - INFO - 成功下载媒体文件: _wan.o7_0030_2025-07-31.jpg
2025-07-31 23:11:36,820 - INFO - 成功下载媒体文件: _wan.o7_0031_2025-07-31.jpg
2025-07-31 23:11:39,293 - INFO - 成功下载媒体文件: _wan.o7_0032_2025-07-31.jpg
2025-07-31 23:11:39,832 - INFO - 成功下载媒体文件: _wan.o7_0033_2025-07-31.jpg
2025-07-31 23:11:40,362 - INFO - 成功下载媒体文件: _wan.o7_0034_2025-07-31.jpg
2025-07-31 23:11:40,919 - INFO - 成功下载媒体文件: _wan.o7_0035_2025-07-31.jpg
2025-07-31 23:11:41,458 - INFO - 成功下载媒体文件: _wan.o7_0036_2025-07-31.jpg
2025-07-31 23:11:41,976 - INFO - 成功下载媒体文件: _wan.o7_0037_2025-07-31.jpg
2025-07-31 23:11:42,522 - INFO - 成功下载媒体文件: _wan.o7_0038_2025-07-31.jpg
2025-07-31 23:11:43,151 - INFO - 成功下载媒体文件: _wan.o7_0039_2025-07-31.jpg
2025-07-31 23:11:43,656 - INFO - 成功下载媒体文件: _wan.o7_0040_2025-07-31.jpg
2025-07-31 23:11:44,193 - INFO - 成功下载媒体文件: _wan.o7_0041_2025-07-31.jpg
2025-07-31 23:11:44,715 - INFO - 成功下载媒体文件: _wan.o7_0042_2025-07-31.jpg
2025-07-31 23:11:46,116 - INFO - 成功下载媒体文件: _wan.o7_0043_2025-07-31.jpg
2025-07-31 23:11:46,643 - INFO - 成功下载媒体文件: _wan.o7_0044_2025-07-31.jpg
2025-07-31 23:11:47,163 - INFO - 成功下载媒体文件: _wan.o7_0045_2025-07-31.jpg
2025-07-31 23:11:47,728 - INFO - 成功下载媒体文件: _wan.o7_0046_2025-07-31.jpg
2025-07-31 23:11:48,274 - INFO - 成功下载媒体文件: _wan.o7_0047_2025-07-31.jpg
2025-07-31 23:11:48,802 - INFO - 成功下载媒体文件: _wan.o7_0048_2025-07-31.jpg
2025-07-31 23:11:49,317 - INFO - 成功下载媒体文件: _wan.o7_0049_2025-07-31.jpg
2025-07-31 23:11:49,830 - INFO - 成功下载媒体文件: _wan.o7_0050_2025-07-31.jpg
2025-07-31 23:11:50,346 - INFO - 成功下载媒体文件: _wan.o7_0051_2025-07-31.jpg
2025-07-31 23:11:50,862 - INFO - 成功下载媒体文件: _wan.o7_0052_2025-07-31.jpg
2025-07-31 23:11:51,374 - INFO - 成功下载媒体文件: _wan.o7_0053_2025-07-31.jpg
2025-07-31 23:11:51,375 - INFO - 正在保存对话数据...
2025-07-31 23:11:51,377 - INFO - 对话数据已保存到: output\instagram_dm__wan.o7_20250731_231151.json
2025-07-31 23:11:51,378 - INFO - 成功提取并保存了 118 条消息
2025-07-31 23:11:57,737 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000022784391E50>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')': /session/643e9b24051cfc11d38505372dd43f81
2025-07-31 23:12:01,808 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000022784392710>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')': /session/643e9b24051cfc11d38505372dd43f81
2025-07-31 23:12:05,902 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002278437AEA0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')': /session/643e9b24051cfc11d38505372dd43f81
