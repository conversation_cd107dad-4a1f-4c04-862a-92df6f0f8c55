# -*- coding: utf-8 -*-
"""
Instagram私信数据提取器
使用Selenium自动化浏览器提取Instagram私信对话数据
"""

import time
import json
import os
import re
from datetime import datetime
from typing import List, Dict, Optional
from dataclasses import dataclass, asdict
import logging

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup
import requests
from tqdm import tqdm

from config import Config
from instagram_selectors import InstagramSelectors
from utils import ElementFinder, PopupHandler, DataValidator, FileManager, retry_on_exception, random_delay, human_like_scroll

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('instagram_extractor.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class Message:
    """消息数据结构"""
    id: str
    sender: str
    content: str
    timestamp: str
    message_type: str  # text, image, video, audio
    media_url: Optional[str] = None
    media_path: Optional[str] = None
    is_outgoing: bool = False

@dataclass
class Conversation:
    """对话数据结构"""
    participant1: str
    participant2: str
    messages: List[Message]
    total_messages: int
    date_range: Dict[str, str]
    extraction_date: str

class InstagramExtractor:
    """Instagram私信数据提取器"""
    
    def __init__(self):
        self.config = Config()
        self.driver = None
        self.wait = None
        self.messages = []
        self.media_counter = 0
        self.element_finder = None
        self.popup_handler = None

        # 创建输出目录
        FileManager.ensure_directory(self.config.OUTPUT_DIR)
        FileManager.ensure_directory(self.config.BACKUP_DIR)
        FileManager.ensure_directory(os.path.join(self.config.OUTPUT_DIR, 'media'))
    
    def setup_driver(self) -> webdriver.Chrome:
        """设置Chrome浏览器驱动"""
        logger.info("正在设置Chrome浏览器驱动...")
        
        chrome_options = Options()
        chrome_options.add_argument(f'--user-agent={self.config.USER_AGENT}')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        if self.config.HEADLESS_MODE:
            chrome_options.add_argument('--headless')
        
        # 设置下载目录
        prefs = {
            "download.default_directory": os.path.abspath(os.path.join(self.config.OUTPUT_DIR, 'media')),
            "download.prompt_for_download": False,
            "download.directory_upgrade": True,
            "safebrowsing.enabled": True
        }
        chrome_options.add_experimental_option("prefs", prefs)
        
        # 设置驱动路径
        if self.config.CHROME_DRIVER_PATH:
            service = Service(self.config.CHROME_DRIVER_PATH)
        else:
            service = Service(ChromeDriverManager().install())
        
        driver = webdriver.Chrome(service=service, options=chrome_options)
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        self.driver = driver
        self.wait = WebDriverWait(driver, 10)
        self.element_finder = ElementFinder(driver)
        self.popup_handler = PopupHandler(driver)

        logger.info("Chrome浏览器驱动设置完成")
        return driver
    
    @retry_on_exception(max_retries=3)
    def login_instagram(self) -> bool:
        """登录Instagram"""
        logger.info("正在登录Instagram...")

        try:
            self.driver.get(self.config.INSTAGRAM_LOGIN_URL)
            random_delay(self.config.LOAD_DELAY, self.config.LOAD_DELAY + 1)

            # 处理可能的弹窗
            self.popup_handler.dismiss_popups()

            # 查找并填写用户名
            username_selectors = InstagramSelectors.get_selector('login', 'username_input')
            username_input = self.element_finder.find_element_by_selectors(username_selectors)
            if not username_input:
                logger.error("未找到用户名输入框")
                return False

            if not self.element_finder.safe_send_keys(username_input, self.config.INSTAGRAM_USERNAME):
                logger.error("输入用户名失败")
                return False

            # 查找并填写密码
            password_selectors = InstagramSelectors.get_selector('login', 'password_input')
            password_input = self.element_finder.find_element_by_selectors(password_selectors)
            if not password_input:
                logger.error("未找到密码输入框")
                return False

            if not self.element_finder.safe_send_keys(password_input, self.config.INSTAGRAM_PASSWORD):
                logger.error("输入密码失败")
                return False

            # 查找并点击登录按钮
            login_selectors = InstagramSelectors.get_selector('login', 'login_button')
            login_button = self.element_finder.find_clickable_element(login_selectors)
            if not login_button:
                logger.error("未找到登录按钮")
                return False

            if not self.element_finder.safe_click(login_button):
                logger.error("点击登录按钮失败")
                return False

            # 等待登录完成
            random_delay(self.config.LOAD_DELAY * 2, self.config.LOAD_DELAY * 3)

            # 处理可能的验证弹窗
            self.popup_handler.dismiss_popups()

            # 检查是否需要处理双因素认证或其他验证
            current_url = self.driver.current_url
            if 'challenge' in current_url or 'two_factor' in current_url:
                logger.warning("检测到需要额外验证，请手动完成验证后按回车继续...")
                input("完成验证后按回车继续...")
                random_delay(2, 3)

            # 验证登录是否成功
            if 'instagram.com' in self.driver.current_url and 'login' not in self.driver.current_url:
                logger.info("Instagram登录成功")
                return True
            else:
                logger.error("Instagram登录失败")
                return False

        except Exception as e:
            logger.error(f"登录过程中发生错误: {str(e)}")
            return False
    
    def navigate_to_dm(self, target_username: str) -> bool:
        """导航到指定用户的私信对话"""
        logger.info(f"正在导航到与 {target_username} 的私信对话...")
        
        try:
            # 访问私信页面
            self.driver.get(self.config.INSTAGRAM_DM_URL)
            time.sleep(self.config.LOAD_DELAY)
            
            # 查找目标用户的对话
            # 这里需要根据实际的Instagram页面结构来调整选择器
            conversation_url = f"{self.config.INSTAGRAM_DM_URL}t/{target_username}/"
            self.driver.get(conversation_url)
            time.sleep(self.config.LOAD_DELAY)
            
            logger.info(f"成功导航到与 {target_username} 的对话")
            return True
            
        except Exception as e:
            logger.error(f"导航到私信对话时发生错误: {str(e)}")
            return False

    def scroll_to_load_messages(self) -> None:
        """滚动页面加载更多历史消息"""
        logger.info("正在滚动加载历史消息...")

        last_height = self.driver.execute_script("return document.body.scrollHeight")
        scroll_attempts = 0
        max_attempts = 50  # 最大滚动次数

        while scroll_attempts < max_attempts:
            # 滚动到页面顶部
            self.driver.execute_script("window.scrollTo(0, 0);")
            time.sleep(self.config.SCROLL_DELAY)

            # 检查是否有新内容加载
            new_height = self.driver.execute_script("return document.body.scrollHeight")

            if new_height == last_height:
                # 尝试查找"加载更多"按钮
                try:
                    load_more_button = self.driver.find_element(By.CSS_SELECTOR, self.config.SELECTORS['load_more'])
                    if load_more_button.is_displayed():
                        load_more_button.click()
                        time.sleep(self.config.LOAD_DELAY)
                    else:
                        break
                except NoSuchElementException:
                    break

            last_height = new_height
            scroll_attempts += 1

            # 检查是否已达到最大消息数
            current_messages = len(self.driver.find_elements(By.CSS_SELECTOR, self.config.SELECTORS['message_text']))
            if current_messages >= self.config.MAX_MESSAGES:
                logger.info(f"已达到最大消息数限制: {self.config.MAX_MESSAGES}")
                break

        logger.info(f"滚动完成，共尝试 {scroll_attempts} 次")

    def extract_messages(self) -> List[Message]:
        """提取消息数据"""
        logger.info("正在提取消息数据...")

        messages = []

        try:
            # 获取页面HTML
            page_source = self.driver.page_source
            soup = BeautifulSoup(page_source, 'html.parser')

            # 查找消息容器
            message_containers = soup.find_all('div', {'role': 'grid'})

            for container in message_containers:
                # 提取消息元素
                message_elements = container.find_all('div', recursive=True)

                for element in message_elements:
                    message_data = self._parse_message_element(element)
                    if message_data:
                        messages.append(message_data)

            # 去重并排序
            messages = self._deduplicate_messages(messages)
            messages.sort(key=lambda x: x.timestamp)

            logger.info(f"成功提取 {len(messages)} 条消息")
            return messages

        except Exception as e:
            logger.error(f"提取消息时发生错误: {str(e)}")
            return []

    def _parse_message_element(self, element) -> Optional[Message]:
        """解析单个消息元素"""
        try:
            # 提取消息文本
            text_elements = element.find_all('div', {'dir': 'auto'})
            if not text_elements:
                return None

            content = ""
            for text_elem in text_elements:
                if text_elem.get_text(strip=True):
                    content += text_elem.get_text(strip=True) + " "

            content = content.strip()
            if not content:
                return None

            # 提取时间戳
            time_element = element.find('time')
            timestamp = ""
            if time_element:
                timestamp = time_element.get('datetime', '')
                if not timestamp:
                    timestamp = time_element.get('title', '')

            if not timestamp:
                timestamp = datetime.now().isoformat()

            # 判断发送者（这里需要根据实际页面结构调整）
            is_outgoing = self._is_outgoing_message(element)
            sender = self.config.INSTAGRAM_USERNAME if is_outgoing else self.config.TARGET_USERNAME

            # 检查是否包含媒体
            media_url = None
            message_type = "text"

            img_elements = element.find_all('img')
            video_elements = element.find_all('video')

            if img_elements:
                message_type = "image"
                media_url = img_elements[0].get('src', '')
            elif video_elements:
                message_type = "video"
                media_url = video_elements[0].get('src', '')

            # 生成消息ID
            message_id = f"{sender}_{timestamp}_{hash(content)}"

            return Message(
                id=message_id,
                sender=sender,
                content=content,
                timestamp=timestamp,
                message_type=message_type,
                media_url=media_url,
                is_outgoing=is_outgoing
            )

        except Exception as e:
            logger.debug(f"解析消息元素时发生错误: {str(e)}")
            return None

    def _is_outgoing_message(self, element) -> bool:
        """判断消息是否为发出的消息"""
        # 这里需要根据Instagram的实际页面结构来判断
        # 通常发出的消息和接收的消息在CSS类或位置上有区别

        # 检查父元素的类名或样式
        parent_classes = element.get('class', [])
        if isinstance(parent_classes, list):
            parent_classes = ' '.join(parent_classes)

        # Instagram通常用不同的样式来区分发出和接收的消息
        # 这里是一个示例，需要根据实际情况调整
        outgoing_indicators = ['outgoing', 'sent', 'right', 'primary']

        for indicator in outgoing_indicators:
            if indicator in parent_classes.lower():
                return True

        return False

    def _deduplicate_messages(self, messages: List[Message]) -> List[Message]:
        """去除重复消息"""
        seen_ids = set()
        unique_messages = []

        for message in messages:
            if message.id not in seen_ids:
                seen_ids.add(message.id)
                unique_messages.append(message)

        return unique_messages

    def download_media(self, message: Message) -> Optional[str]:
        """下载消息中的媒体文件"""
        if not message.media_url:
            return None

        try:
            # 生成文件名
            file_extension = self._get_file_extension(message.media_url, message.message_type)
            filename = f"{message.sender}_{self.media_counter:04d}_{message.timestamp[:10]}.{file_extension}"
            filepath = os.path.join(self.config.OUTPUT_DIR, 'media', filename)

            # 下载文件
            headers = {
                'User-Agent': self.config.USER_AGENT,
                'Referer': 'https://www.instagram.com/'
            }

            response = requests.get(message.media_url, headers=headers, stream=True)
            response.raise_for_status()

            with open(filepath, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)

            self.media_counter += 1
            logger.info(f"成功下载媒体文件: {filename}")
            return filepath

        except Exception as e:
            logger.error(f"下载媒体文件失败: {str(e)}")
            return None

    def _get_file_extension(self, url: str, message_type: str) -> str:
        """根据URL和消息类型获取文件扩展名"""
        if message_type == "image":
            return "jpg"
        elif message_type == "video":
            return "mp4"
        else:
            # 尝试从URL中提取扩展名
            match = re.search(r'\.([a-zA-Z0-9]+)(?:\?|$)', url)
            if match:
                return match.group(1)
            return "bin"

    def save_conversation_data(self, messages: List[Message]) -> str:
        """保存对话数据到文件"""
        logger.info("正在保存对话数据...")

        # 创建对话对象
        conversation = Conversation(
            participant1=self.config.INSTAGRAM_USERNAME,
            participant2=self.config.TARGET_USERNAME,
            messages=messages,
            total_messages=len(messages),
            date_range={
                "start": messages[0].timestamp if messages else "",
                "end": messages[-1].timestamp if messages else ""
            },
            extraction_date=datetime.now().isoformat()
        )

        # 保存为JSON格式
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        json_filename = f"instagram_dm_{self.config.TARGET_USERNAME}_{timestamp}.json"
        json_filepath = os.path.join(self.config.OUTPUT_DIR, json_filename)

        with open(json_filepath, 'w', encoding='utf-8') as f:
            json.dump(asdict(conversation), f, ensure_ascii=False, indent=2)

        logger.info(f"对话数据已保存到: {json_filepath}")
        return json_filepath

    def extract_conversation(self, target_username: Optional[str] = None) -> bool:
        """提取完整对话的主要方法"""
        target_username = target_username or self.config.TARGET_USERNAME

        if not target_username:
            logger.error("未指定目标用户名")
            return False

        try:
            # 设置浏览器
            self.setup_driver()

            # 登录Instagram
            if not self.login_instagram():
                return False

            # 导航到私信对话
            if not self.navigate_to_dm(target_username):
                return False

            # 滚动加载历史消息
            self.scroll_to_load_messages()

            # 提取消息
            messages = self.extract_messages()

            if not messages:
                logger.warning("未提取到任何消息")
                return False

            # 下载媒体文件
            logger.info("正在下载媒体文件...")
            for message in tqdm(messages, desc="下载媒体"):
                if message.media_url:
                    media_path = self.download_media(message)
                    message.media_path = media_path

            # 保存数据
            self.save_conversation_data(messages)

            logger.info(f"成功提取并保存了 {len(messages)} 条消息")
            return True

        except Exception as e:
            logger.error(f"提取对话时发生错误: {str(e)}")
            return False

        finally:
            if self.driver:
                self.driver.quit()

    def __del__(self):
        """析构函数，确保浏览器正确关闭"""
        if hasattr(self, 'driver') and self.driver:
            try:
                self.driver.quit()
            except:
                pass
