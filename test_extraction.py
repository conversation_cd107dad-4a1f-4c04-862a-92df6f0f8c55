#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据提取质量的脚本
"""

import json
import os
from datetime import datetime
from collections import Counter

def analyze_json_file(file_path):
    """分析JSON文件的数据质量"""
    print(f"\n📊 分析文件: {file_path}")
    print("=" * 60)
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        if 'messages' not in data:
            print("❌ 文件格式错误：缺少 'messages' 字段")
            return
        
        messages = data['messages']
        print(f"📝 总消息数: {len(messages)}")
        
        # 检查重复消息
        content_counts = Counter()
        for msg in messages:
            content = msg.get('content', '').strip()
            if content:
                content_counts[content] += 1
        
        duplicates = {content: count for content, count in content_counts.items() if count > 1}
        print(f"🔄 重复消息数: {len(duplicates)}")
        if duplicates:
            print("   前5个重复最多的消息:")
            for content, count in sorted(duplicates.items(), key=lambda x: x[1], reverse=True)[:5]:
                print(f"   - '{content[:50]}...' (重复{count}次)")
        
        # 检查空消息
        empty_messages = [msg for msg in messages if not msg.get('content', '').strip()]
        print(f"🚫 空消息数: {len(empty_messages)}")
        
        # 检查发送者分布
        sender_counts = Counter()
        for msg in messages:
            sender = msg.get('sender', 'unknown')
            sender_counts[sender] += 1
        
        print(f"👥 发送者分布:")
        for sender, count in sender_counts.items():
            print(f"   - {sender}: {count} 条消息")
        
        # 检查消息类型
        type_counts = Counter()
        for msg in messages:
            msg_type = msg.get('message_type', 'unknown')
            type_counts[msg_type] += 1
        
        print(f"📱 消息类型分布:")
        for msg_type, count in type_counts.items():
            print(f"   - {msg_type}: {count} 条")
        
        # 显示示例消息
        print(f"\n📋 消息示例 (前5条):")
        for i, msg in enumerate(messages[:5]):
            content = msg.get('content', '')[:100]
            sender = msg.get('sender', 'unknown')
            timestamp = msg.get('timestamp', '')
            print(f"   {i+1}. [{sender}] {content}... (时间: {timestamp})")
        
        # 检查时间戳格式
        valid_timestamps = 0
        for msg in messages:
            timestamp = msg.get('timestamp', '')
            if timestamp and 'T' in timestamp:
                valid_timestamps += 1
        
        print(f"⏰ 有效时间戳: {valid_timestamps}/{len(messages)}")
        
        # 数据质量评分
        quality_score = 0
        total_checks = 5
        
        if len(duplicates) < len(messages) * 0.1:  # 重复率小于10%
            quality_score += 1
        if len(empty_messages) < len(messages) * 0.05:  # 空消息率小于5%
            quality_score += 1
        if len(sender_counts) >= 2:  # 至少有两个发送者
            quality_score += 1
        if valid_timestamps > len(messages) * 0.8:  # 80%以上有有效时间戳
            quality_score += 1
        if len(messages) > 10:  # 至少有10条消息
            quality_score += 1
        
        print(f"\n⭐ 数据质量评分: {quality_score}/{total_checks}")
        
        if quality_score >= 4:
            print("✅ 数据质量良好")
        elif quality_score >= 2:
            print("⚠️ 数据质量一般，建议优化")
        else:
            print("❌ 数据质量较差，需要修复")
            
    except Exception as e:
        print(f"❌ 分析文件时出错: {e}")

def find_latest_json_file():
    """查找最新的JSON文件"""
    output_dir = "output"
    if not os.path.exists(output_dir):
        return None
    
    json_files = []
    for file in os.listdir(output_dir):
        if file.endswith('.json') and 'instagram_dm_' in file:
            file_path = os.path.join(output_dir, file)
            mtime = os.path.getmtime(file_path)
            json_files.append((file_path, mtime))
    
    if not json_files:
        return None
    
    # 返回最新的文件
    json_files.sort(key=lambda x: x[1], reverse=True)
    return json_files[0][0]

def main():
    print("🔍 Instagram消息数据质量分析工具")
    print("=" * 60)
    
    # 查找最新的JSON文件
    latest_file = find_latest_json_file()
    
    if latest_file:
        analyze_json_file(latest_file)
    else:
        print("❌ 未找到任何Instagram消息JSON文件")
        print("请先运行数据提取程序")
    
    # 也可以分析指定文件
    print("\n" + "=" * 60)
    custom_file = input("输入要分析的JSON文件路径 (回车跳过): ").strip()
    if custom_file and os.path.exists(custom_file):
        analyze_json_file(custom_file)

if __name__ == "__main__":
    main()
